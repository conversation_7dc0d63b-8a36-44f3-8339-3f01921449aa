# 更新日志

本文档记录了 Pixiv Tag Downloader 的所有重要变更。

格式基于 [Keep a Changelog](https://keepachangelog.com/zh-CN/1.0.0/)，
并且本项目遵循 [语义化版本](https://semver.org/lang/zh-CN/)。

## [未发布]

### 计划中的功能
- 支持收藏夹下载
- 支持关注用户批量下载
- 添加下载历史记录
- 支持更多文件格式
- 添加下载统计功能

## [0.1.0] - 2024-01-01

### 新增
- 🎉 初始版本发布
- 🖥️ GUI 模式：使用 Slint 框架构建的图形用户界面
  - 直观的用户信息输入界面
  - 可视化标签选择和筛选
  - 实时下载进度显示
  - 配置管理界面
- 💻 TUI 模式：交互式终端界面
  - 用户友好的终端交互
  - 彩色进度条和状态显示
  - 支持键盘导航
- ⌨️ CLI 模式：命令行自动化接口
  - 支持批量下载脚本
  - 完整的参数配置
  - 适合服务器环境使用
- 🎨 多种作品类型支持
  - 插画 (Illust) 下载
  - 漫画 (Manga) 多页下载
  - 小说 (Novel) 文本下载
- 🏷️ 智能标签筛选系统
  - 支持与 (AND) 逻辑筛选
  - 支持或 (OR) 逻辑筛选
  - 自动提取所有可用标签
- ⚡ 高性能异步下载引擎
  - 基于 Tokio 的异步并发下载
  - 可配置的并发数量控制
  - 智能请求延迟机制
- 🔄 可靠的错误处理和重试
  - 自动重试失败的下载
  - 可配置的重试次数和延迟
  - 详细的错误日志记录
- 📁 灵活的文件组织系统
  - 完全可定制的目录结构
  - 模板化文件命名
  - 支持多种模板变量
- 💾 断点续传支持
  - 自动检测部分下载的文件
  - 从中断位置继续下载
  - 避免重复下载
- 📊 元数据管理
  - 自动生成作品元数据文件
  - 包含作品详细信息
  - 支持自定义元数据格式
- ⚙️ 完整的配置系统
  - YAML 格式配置文件
  - 自动生成示例配置
  - 配置验证和错误提示
- 🛡️ 安全和合规性
  - 内置请求延迟机制
  - Cookie 认证验证
  - 用户协议提醒

### 技术特性
- 🦀 使用 Rust 语言开发，保证性能和安全性
- 🎯 模块化架构设计，易于维护和扩展
- 🧪 完整的单元测试和集成测试覆盖
- 📚 详细的中文文档和注释
- 🔧 支持跨平台编译 (Windows, macOS, Linux)

### 依赖库
- `tokio` - 异步运行时
- `reqwest` - HTTP 客户端
- `slint` - GUI 框架
- `clap` - 命令行解析
- `dialoguer` - TUI 交互
- `serde` - 序列化支持
- `indicatif` - 进度条显示
- `log` - 日志系统

### 配置选项
- 认证配置：Cookie、User-Agent、超时设置
- 下载配置：并发数、延迟范围、重试设置
- 存储配置：输出目录、命名模板、冲突处理
- 日志配置：级别、文件输出设置

### 支持的模板变量
- `{uid}` - 用户ID
- `{username}` - 用户名
- `{pid}` - 作品ID
- `{title}` - 作品标题
- `{page_index}` - 页面索引
- `{page_indexzfill3}` - 零填充页面索引
- `{date}` - 发布日期
- `{tags}` - 标签列表
- `{artwork_type}` - 作品类型
- `{extension}` - 文件扩展名

### 文件冲突处理策略
- `skip` - 跳过已存在的文件（默认）
- `overwrite` - 覆盖已存在的文件
- `rename` - 自动重命名新文件

### 已知限制
- 需要有效的 Pixiv Cookie 进行认证
- 依赖于 Pixiv API 的稳定性
- 仅支持公开作品的下载
- 不支持需要付费的作品

### 性能指标
- 支持最大 10 个并发下载连接
- 内置 1-3 秒随机延迟机制
- 支持断点续传，减少重复下载
- 内存使用优化，适合长时间运行

### 安全考虑
- Cookie 信息本地存储，不会上传
- 遵循 Pixiv 的访问频率限制
- 提供用户协议和版权提醒
- 支持日志审计和问题排查
