[package]
name = "PixivTagDownloader"
version = "0.1.0"
edition = "2021"
authors = ["Pixiv Tag Downloader Team"]
description = "A high-performance, configurable Rust application for downloading Pixiv artworks by user ID"
license = "MIT"

[[bin]]
name = "PixivTagDownloader"
path = "src/main.rs"

[dependencies]
# 异步运行时
tokio = { version = "1.0", features = ["full"] }

# HTTP 客户端
reqwest = { version = "0.11", features = ["json", "cookies", "stream"] }

# 序列化和配置
serde = { version = "1.0", features = ["derive"] }
serde_yaml = "0.9"
serde_json = "1.0"

# 命令行解析
clap = { version = "4.0", features = ["derive"] }

# TUI 交互
dialoguer = "0.11"
console = "0.15"
indicatif = "0.17"

# GUI 框架 (暂时注释掉以加快编译)
# slint = "1.3"

# 日志系统
log = "0.4"
env_logger = "0.10"

# 错误处理
anyhow = "1.0"
thiserror = "1.0"

# 文件系统操作
uuid = { version = "1.0", features = ["v4"] }
chrono = { version = "0.4", features = ["serde"] }
regex = "1.0"

# 异步文件操作
tokio-util = { version = "0.7", features = ["io"] }
futures = "0.3"

# [build-dependencies]
# slint-build = "1.3"

[dev-dependencies]
tempfile = "3.0"
tokio-test = "0.4"
