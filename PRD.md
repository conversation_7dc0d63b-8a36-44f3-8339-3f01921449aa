# Pixiv Tag Downloader - 产品需求文档 (PRD)

## 1. 项目概述

本项目旨在开发一个高效、可配置的 Rust 应用程序（可执行程序命名为 `PixivTagDownloader`），用于根据用户指定的 Pixiv 用户ID (UID) 下载其发布的各类作品（包括插画、漫画、小说）。

程序的核心功能通过一个中央 YAML 配置文件 `config.yaml` 进行驱动。为满足不同用户群体的需求，程序提供三种独立的操作模式：
1.  图形用户界面 (GUI) 一个使用 Slint 框架构建的、直观易用的桌面应用。
2.  交互式终端界面 (TUI) 一个无需任何命令行参数即可启动、功能完善的文本界面。
3.  命令行接口 (CLI) 一个支持通过命令行参数进行快速、自动化任务执行的接口。

为了提升下载效率和稳定性，程序将采用 Rust 的异步机制 (Tokio) 进行并发下载，并内置可配置的随机延迟与重试机制。作品的存储结构和文件命名将实现完全的模板化自定义，并为不同类型的作品提供独立的配置规则。

本项目强调模块化、高内聚与低耦合的设计，旨在利用 Rust 语言的特性构建一个健壮、安全、易于维护和扩展的工具。

## 2. 术语定义

-   UID Pixiv 用户的唯一标识符。
-   PID Pixiv 作品（插画、漫画、小说等）的唯一标识符。
-   GUI (Graphical User Interface) 图形用户界面。
-   TUI (Text-based User Interface) 基于文本的交互式用户界面。
-   Slint 一个用于构建原生 GUI 的声明式 UI 工具包。
-   YAML 一种人性化、易于阅读的数据序列化格式，用于配置文件。
-   元数据 (Metadata) 描述作品属性的数据。
-   模板变量 (Template Variable) 在文件和目录命名规则中使用的占位符。

## 3. 功能需求 (Functional Requirements)

### 3.1 配置与认证 (FR-CONF)

-   FR-CONF-01 程序启动时，将首先在当前工作目录或由 `--config PATH` 参数指定的路径下查找名为 `config.yaml` 的配置文件。
-   FR-CONF-02 配置文件自动生成 如果 `config.yaml` 文件不存在，程序将自动创建一个名为 `config.yaml.example` 的示例文件。该文件包含所有可配置项、详细的中文注释和默认值。随后，程序将提示用户根据此模板创建并填写自己的 `config.yaml` 文件，然后正常退出。
-   FR-CONF-03 程序必须从 `config.yaml` 文件中读取 `auth.cookie` 字段作为用户认证凭据。如果该字段为空或缺失，程序应给出明确的错误提示并终止执行。
-   FR-CONF-04 程序在执行任何需要登录的操作前，应使用配置的 Cookie 尝试访问一个需授权的 API 端点，以验证 Cookie 的有效性。若验证失败，应提示用户检查 Cookie 配置并终止。

### 3.2 用户交互与作品筛选 (FR-UI) &nbsp; (适用于 TUI 模式)

-   FR-UI-01 在 TUI 模式下，程序成功认证后，应提示用户输入目标用户的 Pixiv UID。
-   FR-UI-02 在获取到指定 UID 的作品数据后，程序应提取并展示该用户所有作品中包含的唯一标签（Tag）列表，并允许用户交互式地单选或多选。
-   FR-UI-03 程序应允许用户指定标签的筛选逻辑：“与”(AND) 或 “或”(OR)。
-   FR-UI-04 用户可选择需要下载的作品类型（`Illust`, `Manga`, `Novel`），支持单选或多选。
-   FR-UI-05 在下载过程中，必须提供清晰的进度反馈。
-   FR-UI-06 所有面向用户的提示、选项和错误信息均须使用简体中文。

### 3.3 数据获取与处理 (FR-DATA)

-   FR-DATA-01 程序必须能根据 UID 获取用户的公开信息，特别是用户名，用于文件命名。
-   FR-DATA-02 程序需能获取指定用户发布的所有作品列表及其详细元数据，并自动处理分页。
-   FR-DATA-03 作品类型判断 作品的类型（`Illust`, `Manga`, `Novel`）必须根据从 API 获取的 `type` 字段来确定，不得依赖作品的图片数量进行猜测。
-   FR-DATA-04 对于包含多页内容的作品，程序必须能获取所有页面的原始图片链接，并确保下载时顺序正确。
-   FR-DATA-05 需根据用户选择的标签和筛选逻辑，精确过滤出待下载的作品列表。

### 3.4 下载核心 (FR-DL)

-   FR-DL-01 必须使用异步运行时（如 `tokio`）实现并发下载。最大并发任务数需在配置文件中可调。
-   FR-DL-02 每次网络请求之间必须加入一个可配置的随机延迟。
-   FR-DL-03 下载过程中应能妥善处理网络错误，并根据配置执行重试。
-   FR-DL-04 支持断点续传。

### 3.5 文件存储与组织 (FR-STORE)

-   FR-STORE-01 下载内容的根目录可在配置文件或通过命令行参数 `--output-dir` 指定。

-   FR-STORE-02 类型独立的存储规则 配置文件中必须允许为不同类型的作品（`Illust`, `Manga`, `Novel`）分别独立地指定文件存储路径和文件命名规则。

-   FR-STORE-03 文件和目录命名模板必须支持丰富的变量（如 `{uid}`, `{pid}`, `{title}`, `{page_indexzfill3}` 等）。

-   FR-STORE-04 元数据文件生成
    -   对于单页作品：在图片文件旁创建同名的 `.txt` 元数据文件。
    -   对于多页作品：在该作品的专属目录内，创建单一的元数据文件（如 `metadata.txt`）。

-   FR-STORE-05 小说文件生成 小说将保存为 `.txt` 文件，头部包含元数据，后跟正文。

-   FR-STORE-06 文件冲突处理 配置文件中需提供文件已存在时的处理策略：`skip` (默认)、`overwrite`、`rename`。

-   FR-STORE-07 文件名和路径中的非法字符必须被自动替换或移除。

### 3.6 程序执行模式 (FR-EXEC)

-   FR-EXEC-01 启动逻辑
    1.  若使用 `--gui` 标志，则启动 GUI 模式。
    2.  若提供了 `-u, --uid` 等任务参数，则进入 CLI 模式。
    3.  若不提供任何参数，则默认进入 TUI 模式。

-   FR-EXEC-02 命令行接口 (CLI) 必须支持通过 `clap` 等工具库解析命令行参数，以实现非交互式、自动化的任务执行（如 `-u`, `--tags`, `--type` 等）。

### 3.7 图形用户界面 (GUI) (FR-GUI)

-   FR-GUI-01 UI 技术栈 GUI 必须使用 Slint 框架进行开发，确保跨平台（Windows, macOS, Linux）的原生观感和性能。

-   FR-GUI-02 主窗口布局 GUI 主窗口应清晰地划分功能区域：
    -   输入区 用于输入用户 UID，并提供一个“获取信息”按钮。
    -   筛选区
        -   以可滚动的复选框列表形式，展示获取到的所有标签。
        -   提供“全选”、“清空”和文本框搜索功能来辅助标签选择。
        -   提供单选按钮组用于选择标签筛选逻辑（“与”“或”）。
        -   提供复选框组用于选择要下载的作品类型（`Illust`, `Manga`, `Novel`）。
    -   任务控制区 包含“开始下载”按钮。
    -   状态与进度区
        -   一个总体进度条，显示整个下载队列的完成度。
        -   一个可滚动的列表或表格，实时展示每个下载项（文件名、大小、状态：排队中、下载中、完成、失败）。
        -   一个日志输出面板，显示程序运行的详细信息和错误。

-   FR-GUI-03 配置管理
    -   程序必须提供一个独立的“设置”窗口或页面。
    -   此页面应能以图形化的方式展示并允许用户修改 `config.yaml` 中的所有主要配置项（如 Cookie、下载路径、并发数、延迟时间、命名模板等）。
    -   提供“保存”按钮，将用户的修改写回 `config.yaml` 文件。

-   FR-GUI-04 用户体验
    -   所有网络请求和下载任务必须在后台线程执行，确保 GUI 界面永不冻结。
    -   界面元素（如按钮）的状态应根据程序逻辑动态变化（例如，在未输入 UID 时，“获取信息”按钮为禁用状态）。
    -   提供清晰的视觉反馈，如加载动画、错误弹窗等。

## 4. 非功能需求 (Non-Functional Requirements)

-   NFR-DESIGN-01 (模块化) 代码结构应清晰划分为 `config`, `api`, `downloader`, `storage`, `gui`, `tui`, `cli` 等独立模块。`gui`, `tui`, `cli` 模块必须复用底层的核心业务逻辑模块。
-   NFR-PERF-01 (性能) 充分利用异步 IO 和并发处理，实现高效的下载性能和低资源占用。
-   NFR-ROBUST-01 (健壮性) 全面使用 `ResultT, E` 和 `OptionT` 进行错误处理，日志中需记录详细的错误信息以供排查。
-   NFR-USABILITY-01 (易用性) GUI 界面应直观、响应迅速。TUI 界面操作清晰。CLI 参数设计符合标准。所有面向用户的文本必须使用简体中文。
-   NFR-MAINTAIN-01 (可维护性) 遵循 `rustfmt` 和 `clippy` 的代码规范。代码中必须添加完善、详细的简体中文注释。
-   NFR-CONFIG-01 (可配置性) 程序的核心行为由 `config.yaml` 驱动。命令行参数的优先级高于配置文件，配置文件高于程序内置的默认值。
-   NFR-LOGGING-01 (日志) 实现分级别的日志系统，日志可同时输出到控制台、GUI 日志面板和或指定的文件。
-   NFR-PACKAGE-01 (可分发性) 最终应为各主流平台提供单一的可执行文件。需注意 Slint 的编译依赖和打包方式。
-   NFR-COMPLIANCE-01 (合规性) 程序必须内置合理的请求延迟，并向用户明确提示应遵守 Pixiv 的条款，并自行承担版权和法律责任。

## 5. 约束与假设 (Constraints & Assumptions)

-   CON-LIB-01 (核心库)
    -   GUI `slint`
    -   HTTP客户端 `reqwest`
    -   异步运行时 `tokio`
    -   CLI解析 `clap`
    -   TUI交互 `dialoguer` 或类似库
    -   序列化 `serde`, `serde_yaml`
-   CON-PIXIV-01 程序功能强依赖于 Pixiv 的现有 API。若 API 发生变更，可能需要更新程序。
-   CON-COOKIE-01 假设用户在 `config.yaml` 中提供的 Cookie 是有效的。程序不负责获取 Cookie。
-   CON-LEGAL-01 本程序仅供个人学习、研究和合法备份。严禁用于任何商业或侵权行为。