# Pixiv Tag Downloader

一个高效、可配置的 Rust 应用程序，用于根据用户指定的 Pixiv 用户ID (UID) 下载其发布的各类作品（包括插画、漫画、小说）。

## 特性

- 🎨 **多种作品类型支持**：插画、漫画、小说
- 🖥️ **三种操作模式**：GUI（图形界面）、TUI（终端界面）、CLI（命令行）
- ⚡ **高性能下载**：异步并发下载，支持断点续传
- 🏷️ **智能标签筛选**：支持与/或逻辑的标签筛选
- 📁 **灵活的文件组织**：完全可定制的目录结构和文件命名
- 🔄 **自动重试机制**：网络错误自动重试，提高下载成功率
- 📊 **实时进度显示**：详细的下载进度和状态反馈
- 🛡️ **安全可靠**：内置延迟机制，遵守网站访问规范

## 安装

### 从源码编译

确保您已安装 Rust 工具链（推荐使用 rustup）：

```bash
# 克隆仓库
git clone https://github.com/your-username/PixivTagDownloader_Rust.git
cd PixivTagDownloader_Rust

# 编译
cargo build --release

# 可执行文件位于 target/release/PixivTagDownloader
```

### 系统要求

- Rust 1.70 或更高版本
- 对于 GUI 模式，需要系统支持图形界面

## 配置

首次运行程序时，会自动创建 `config.yaml.example` 示例配置文件。请根据此模板创建您的 `config.yaml` 配置文件。

### 获取 Pixiv Cookie

1. 在浏览器中登录 Pixiv
2. 打开开发者工具（F12）
3. 转到 Network 标签页
4. 刷新页面
5. 找到任意请求，复制 Cookie 头部的值
6. 将 Cookie 值填入配置文件的 `auth.cookie` 字段

### 配置示例

```yaml
# 认证配置
auth:
  cookie: "your_pixiv_cookie_here"  # 必填：从浏览器获取的 Pixiv Cookie
  user_agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
  timeout: 30

# 下载配置
download:
  max_concurrent: 3        # 最大并发下载数
  delay_range: [1000, 3000]  # 请求间随机延迟范围（毫秒）
  max_retries: 3          # 最大重试次数
  retry_delay: 5000       # 重试延迟（毫秒）
  enable_resume: true     # 启用断点续传

# 存储配置
storage:
  output_dir: "./downloads"  # 输出根目录
  
  # 插画存储配置
  illust:
    directory_template: "{username}({uid})/插画"
    filename_template: "{title}({pid})"
    metadata_template: "metadata.txt"
  
  # 漫画存储配置
  manga:
    directory_template: "{username}({uid})/漫画/{title}({pid})"
    filename_template: "{title}({pid})_p{page_indexzfill3}"
    metadata_template: "metadata.txt"
  
  # 小说存储配置
  novel:
    directory_template: "{username}({uid})/小说"
    filename_template: "{title}({pid}).txt"
    include_metadata_header: true
  
  conflict_strategy: "skip"  # 文件冲突处理：skip/overwrite/rename
  generate_metadata: true    # 是否生成元数据文件

# 日志配置
logging:
  level: "info"
  log_to_file: false
  log_file: "pixiv_downloader.log"
```

## 使用方法

### GUI 模式（推荐）

启动图形界面：

```bash
./PixivTagDownloader --gui
```

GUI 模式提供直观的用户界面，包括：
- 用户信息输入和获取
- 可视化标签选择
- 实时下载进度显示
- 配置管理界面

### TUI 模式

启动交互式终端界面：

```bash
./PixivTagDownloader
```

TUI 模式提供终端中的交互式体验，适合服务器环境使用。

### CLI 模式

命令行自动化下载：

```bash
# 下载指定用户的所有作品
./PixivTagDownloader --uid 123456

# 按标签筛选下载
./PixivTagDownloader --uid 123456 --tags "风景,自然" --tag-logic and

# 指定作品类型
./PixivTagDownloader --uid 123456 --types "illust,manga"

# 自定义输出目录
./PixivTagDownloader --uid 123456 --output-dir "/path/to/downloads"
```

### 命令行参数

```
选项:
  -u, --uid <UID>              目标用户的 Pixiv UID
      --tags <TAGS>            标签筛选（逗号分隔）
      --tag-logic <LOGIC>      标签筛选逻辑：and 或 or [默认: and]
      --types <TYPES>          作品类型：illust,manga,novel [默认: 全部]
  -c, --config <PATH>          配置文件路径 [默认: config.yaml]
  -o, --output-dir <DIR>       输出目录
      --gui                    启动 GUI 模式
      --log-level <LEVEL>      日志级别 [默认: info]
  -h, --help                   显示帮助信息
  -V, --version                显示版本信息
```

## 模板变量

文件和目录命名支持以下模板变量：

- `{uid}` - 用户ID
- `{username}` - 用户名
- `{pid}` - 作品ID
- `{title}` - 作品标题
- `{page_index}` - 页面索引（从0开始）
- `{page_indexzfill3}` - 页面索引（3位数字，前导零）
- `{date}` - 发布日期
- `{tags}` - 标签列表
- `{artwork_type}` - 作品类型
- `{extension}` - 文件扩展名

## 开发

### 运行测试

```bash
# 运行所有测试
cargo test

# 运行特定测试
cargo test test_config_validation

# 运行集成测试
cargo test --test integration_tests
```

### 代码格式化

```bash
cargo fmt
```

### 代码检查

```bash
cargo clippy
```

## 注意事项

⚠️ **重要提醒**：

1. **合法使用**：本工具仅供个人学习、研究和合法备份使用，严禁用于任何商业或侵权行为
2. **遵守条款**：使用时请遵守 Pixiv 的服务条款和使用协议
3. **适度使用**：程序内置了合理的请求延迟，请勿修改为过于频繁的请求
4. **版权尊重**：下载的内容版权归原作者所有，请尊重创作者的权益
5. **Cookie 安全**：请妥善保管您的 Cookie 信息，不要分享给他人

## 故障排除

### 常见问题

**Q: 提示 Cookie 认证失败**
A: 请检查配置文件中的 Cookie 是否正确，Cookie 可能已过期，需要重新获取。

**Q: 下载速度很慢**
A: 可以适当增加 `max_concurrent` 并发数，但不建议设置过高以避免被限制。

**Q: 某些文件下载失败**
A: 程序会自动重试，如果仍然失败，可能是网络问题或文件已被删除。

**Q: GUI 模式无法启动**
A: 确保系统支持图形界面，在服务器环境请使用 TUI 或 CLI 模式。

### 日志调试

启用详细日志：

```bash
./PixivTagDownloader --log-level debug
```

## 许可证

本项目采用 MIT 许可证。详见 [LICENSE](LICENSE) 文件。

## 贡献

欢迎提交 Issue 和 Pull Request！

## 更新日志

### v0.1.0
- 初始版本发布
- 支持 GUI、TUI、CLI 三种模式
- 实现异步并发下载
- 支持标签筛选和作品类型筛选
- 完整的配置系统和模板化命名
