/*!
 * Pixiv API 客户端模块
 * 
 * 负责与 Pixiv API 进行交互，获取用户信息和作品数据
 */

use anyhow::Result;
use reqwest::{Client, Response};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::time::Duration;
use tokio::time::sleep;
use log::{debug, info, warn, error};
use rand::Rng;

use crate::config::{Config, AuthConfig, DownloadConfig};

/// API 错误类型
#[derive(thiserror::Error, Debug)]
pub enum ApiError {
    #[error("网络请求失败: {0}")]
    Network(#[from] reqwest::Error),
    
    #[error("JSON 解析失败: {0}")]
    JsonParse(#[from] serde_json::Error),
    
    #[error("认证失败: {0}")]
    Authentication(String),
    
    #[error("用户不存在: {0}")]
    UserNotFound(u64),
    
    #[error("API 限制: {0}")]
    RateLimit(String),
    
    #[error("其他 API 错误: {0}")]
    Other(String),
}

/// 作品类型
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
#[serde(rename_all = "lowercase")]
pub enum ArtworkType {
    Illust,
    Manga,
    Novel,
}

impl std::fmt::Display for ArtworkType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            ArtworkType::Illust => write!(f, "插画"),
            ArtworkType::Manga => write!(f, "漫画"),
            ArtworkType::Novel => write!(f, "小说"),
        }
    }
}

/// 用户信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserInfo {
    pub user_id: u64,
    pub name: String,
    pub account: String,
    pub profile_image_urls: HashMap<String, String>,
    pub comment: String,
    pub is_followed: bool,
}

/// 作品信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Artwork {
    pub id: u64,
    pub title: String,
    pub artwork_type: ArtworkType,
    pub description: String,
    pub create_date: String,
    pub page_count: u32,
    pub width: u32,
    pub height: u32,
    pub sanity_level: u32,
    pub x_restrict: u32,
    pub tags: Vec<String>,
    pub user: UserInfo,
    pub image_urls: Vec<String>,
    pub original_image_urls: Vec<String>,
}

/// 作品列表响应
#[derive(Debug, Deserialize)]
struct ArtworkListResponse {
    error: bool,
    message: String,
    body: Option<ArtworkListBody>,
}

#[derive(Debug, Deserialize)]
struct ArtworkListBody {
    works: Vec<ArtworkData>,
    #[serde(rename = "extraData")]
    extra_data: ExtraData,
}

#[derive(Debug, Deserialize)]
struct ExtraData {
    meta: MetaData,
}

#[derive(Debug, Deserialize)]
struct MetaData {
    total: u32,
    page: u32,
    #[serde(rename = "perPage")]
    per_page: u32,
}

#[derive(Debug, Deserialize)]
struct ArtworkData {
    id: String,
    title: String,
    #[serde(rename = "illustType")]
    illust_type: u32,
    #[serde(rename = "xRestrict")]
    x_restrict: u32,
    restrict: u32,
    sl: u32,
    url: String,
    description: String,
    tags: Vec<String>,
    #[serde(rename = "userId")]
    user_id: String,
    #[serde(rename = "userName")]
    user_name: String,
    width: u32,
    height: u32,
    #[serde(rename = "pageCount")]
    page_count: u32,
    #[serde(rename = "isBookmarkable")]
    is_bookmarkable: bool,
    #[serde(rename = "bookmarkData")]
    bookmark_data: Option<serde_json::Value>,
    #[serde(rename = "alt")]
    alt: String,
    #[serde(rename = "titleCaptionTranslation")]
    title_caption_translation: HashMap<String, serde_json::Value>,
    #[serde(rename = "createDate")]
    create_date: String,
    #[serde(rename = "updateDate")]
    update_date: String,
    #[serde(rename = "isUnlisted")]
    is_unlisted: bool,
    #[serde(rename = "isMasked")]
    is_masked: bool,
    #[serde(rename = "profileImageUrl")]
    profile_image_url: String,
}

/// Pixiv API 客户端
pub struct PixivClient {
    client: Client,
    auth_config: AuthConfig,
    download_config: DownloadConfig,
}

impl PixivClient {
    /// 创建新的 API 客户端
    pub fn new(config: &Config) -> Result<Self, ApiError> {
        let client = Client::builder()
            .timeout(Duration::from_secs(config.auth.timeout))
            .user_agent(&config.auth.user_agent)
            .build()
            .map_err(ApiError::Network)?;
            
        Ok(Self {
            client,
            auth_config: config.auth.clone(),
            download_config: config.download.clone(),
        })
    }
    
    /// 验证认证状态
    pub async fn verify_auth(&self) -> Result<bool, ApiError> {
        debug!("验证 Pixiv 认证状态");
        
        let response = self.client
            .get("https://www.pixiv.net/ajax/user/extra")
            .header("Cookie", &self.auth_config.cookie)
            .header("Referer", "https://www.pixiv.net/")
            .send()
            .await?;
            
        if response.status().is_success() {
            info!("Pixiv 认证验证成功");
            Ok(true)
        } else {
            error!("Pixiv 认证验证失败，状态码: {}", response.status());
            Ok(false)
        }
    }
    
    /// 获取用户信息
    pub async fn get_user_info(&self, uid: u64) -> Result<UserInfo, ApiError> {
        info!("获取用户信息: UID {}", uid);
        
        self.apply_delay().await;
        
        let url = format!("https://www.pixiv.net/ajax/user/{}", uid);
        let response = self.make_request(&url).await?;
        
        let json: serde_json::Value = response.json().await?;
        
        if json["error"].as_bool().unwrap_or(true) {
            let message = json["message"].as_str().unwrap_or("未知错误");
            return Err(ApiError::UserNotFound(uid));
        }
        
        let body = &json["body"];
        Ok(UserInfo {
            user_id: uid,
            name: body["name"].as_str().unwrap_or("").to_string(),
            account: body["account"].as_str().unwrap_or("").to_string(),
            profile_image_urls: HashMap::new(),
            comment: body["comment"].as_str().unwrap_or("").to_string(),
            is_followed: body["isFollowed"].as_bool().unwrap_or(false),
        })
    }
    
    /// 获取用户的所有作品
    pub async fn get_user_artworks(&self, uid: u64) -> Result<Vec<Artwork>, ApiError> {
        info!("获取用户作品列表: UID {}", uid);
        
        let mut all_artworks = Vec::new();
        let mut page = 1;
        let per_page = 48; // Pixiv 默认每页数量
        
        loop {
            self.apply_delay().await;
            
            let url = format!(
                "https://www.pixiv.net/ajax/user/{}/profile/all",
                uid
            );
            
            let response = self.make_request(&url).await?;
            let json: serde_json::Value = response.json().await?;
            
            if json["error"].as_bool().unwrap_or(true) {
                break;
            }
            
            let body = &json["body"];
            
            // 获取插画和漫画 ID
            if let Some(illusts) = body["illusts"].as_object() {
                for (id, _) in illusts {
                    if let Ok(artwork_id) = id.parse::<u64>() {
                        if let Ok(artwork) = self.get_artwork_detail(artwork_id).await {
                            all_artworks.push(artwork);
                        }
                    }
                }
            }
            
            // 获取小说 ID
            if let Some(novels) = body["novels"].as_object() {
                for (id, _) in novels {
                    if let Ok(novel_id) = id.parse::<u64>() {
                        if let Ok(novel) = self.get_novel_detail(novel_id).await {
                            all_artworks.push(novel);
                        }
                    }
                }
            }
            
            break; // profile/all 接口返回所有作品，不需要分页
        }
        
        info!("获取到 {} 个作品", all_artworks.len());
        Ok(all_artworks)
    }
    
    /// 获取作品详细信息
    async fn get_artwork_detail(&self, artwork_id: u64) -> Result<Artwork, ApiError> {
        self.apply_delay().await;
        
        let url = format!("https://www.pixiv.net/ajax/illust/{}", artwork_id);
        let response = self.make_request(&url).await?;
        let json: serde_json::Value = response.json().await?;
        
        if json["error"].as_bool().unwrap_or(true) {
            return Err(ApiError::Other(format!("获取作品 {} 详情失败", artwork_id)));
        }
        
        let body = &json["body"];
        self.parse_artwork_from_json(body).await
    }
    
    /// 获取小说详细信息
    async fn get_novel_detail(&self, novel_id: u64) -> Result<Artwork, ApiError> {
        self.apply_delay().await;
        
        let url = format!("https://www.pixiv.net/ajax/novel/{}", novel_id);
        let response = self.make_request(&url).await?;
        let json: serde_json::Value = response.json().await?;
        
        if json["error"].as_bool().unwrap_or(true) {
            return Err(ApiError::Other(format!("获取小说 {} 详情失败", novel_id)));
        }
        
        let body = &json["body"];
        self.parse_novel_from_json(body).await
    }
    
    /// 发起 HTTP 请求
    async fn make_request(&self, url: &str) -> Result<Response, ApiError> {
        let response = self.client
            .get(url)
            .header("Cookie", &self.auth_config.cookie)
            .header("Referer", "https://www.pixiv.net/")
            .header("Accept", "application/json")
            .send()
            .await?;
            
        if !response.status().is_success() {
            return Err(ApiError::Other(format!(
                "HTTP 请求失败: {} - {}", 
                response.status(), 
                url
            )));
        }
        
        Ok(response)
    }
    
    /// 应用随机延迟
    async fn apply_delay(&self) {
        let (min, max) = self.download_config.delay_range;
        let delay = rand::thread_rng().gen_range(min..=max);
        debug!("应用随机延迟: {}ms", delay);
        sleep(Duration::from_millis(delay)).await;
    }
    
    /// 从 JSON 解析作品信息
    async fn parse_artwork_from_json(&self, json: &serde_json::Value) -> Result<Artwork, ApiError> {
        let id = json["id"].as_str()
            .and_then(|s| s.parse::<u64>().ok())
            .ok_or_else(|| ApiError::Other("无效的作品 ID".to_string()))?;

        let title = json["title"].as_str().unwrap_or("").to_string();
        let description = json["description"].as_str().unwrap_or("").to_string();
        let create_date = json["createDate"].as_str().unwrap_or("").to_string();

        // 根据 illustType 确定作品类型
        let artwork_type = match json["illustType"].as_u64().unwrap_or(0) {
            0 => ArtworkType::Illust,
            1 => ArtworkType::Manga,
            2 => ArtworkType::Novel,
            _ => ArtworkType::Illust,
        };

        let page_count = json["pageCount"].as_u64().unwrap_or(1) as u32;
        let width = json["width"].as_u64().unwrap_or(0) as u32;
        let height = json["height"].as_u64().unwrap_or(0) as u32;
        let sanity_level = json["sl"].as_u64().unwrap_or(0) as u32;
        let x_restrict = json["xRestrict"].as_u64().unwrap_or(0) as u32;

        // 解析标签
        let tags = json["tags"]["tags"].as_array()
            .map(|arr| {
                arr.iter()
                    .filter_map(|tag| tag["tag"].as_str())
                    .map(|s| s.to_string())
                    .collect()
            })
            .unwrap_or_default();

        // 解析用户信息
        let user_id = json["userId"].as_str()
            .and_then(|s| s.parse::<u64>().ok())
            .unwrap_or(0);

        let user = UserInfo {
            user_id,
            name: json["userName"].as_str().unwrap_or("").to_string(),
            account: json["userAccount"].as_str().unwrap_or("").to_string(),
            profile_image_urls: HashMap::new(),
            comment: "".to_string(),
            is_followed: false,
        };

        // 获取图片 URL
        let mut image_urls = Vec::new();
        let mut original_image_urls = Vec::new();

        if page_count == 1 {
            // 单页作品
            if let Some(urls) = json["urls"].as_object() {
                if let Some(thumb) = urls["thumb"].as_str() {
                    image_urls.push(thumb.to_string());
                }
                if let Some(original) = urls["original"].as_str() {
                    original_image_urls.push(original.to_string());
                }
            }
        } else {
            // 多页作品，需要获取所有页面
            for i in 0..page_count {
                if let Ok(page_urls) = self.get_artwork_pages(id, i).await {
                    image_urls.extend(page_urls.0);
                    original_image_urls.extend(page_urls.1);
                }
            }
        }

        Ok(Artwork {
            id,
            title,
            artwork_type,
            description,
            create_date,
            page_count,
            width,
            height,
            sanity_level,
            x_restrict,
            tags,
            user,
            image_urls,
            original_image_urls,
        })
    }

    /// 从 JSON 解析小说信息
    async fn parse_novel_from_json(&self, json: &serde_json::Value) -> Result<Artwork, ApiError> {
        let id = json["id"].as_str()
            .and_then(|s| s.parse::<u64>().ok())
            .ok_or_else(|| ApiError::Other("无效的小说 ID".to_string()))?;

        let title = json["title"].as_str().unwrap_or("").to_string();
        let description = json["description"].as_str().unwrap_or("").to_string();
        let create_date = json["createDate"].as_str().unwrap_or("").to_string();

        // 解析标签
        let tags = json["tags"]["tags"].as_array()
            .map(|arr| {
                arr.iter()
                    .filter_map(|tag| tag["tag"].as_str())
                    .map(|s| s.to_string())
                    .collect()
            })
            .unwrap_or_default();

        // 解析用户信息
        let user_id = json["userId"].as_str()
            .and_then(|s| s.parse::<u64>().ok())
            .unwrap_or(0);

        let user = UserInfo {
            user_id,
            name: json["userName"].as_str().unwrap_or("").to_string(),
            account: "".to_string(),
            profile_image_urls: HashMap::new(),
            comment: "".to_string(),
            is_followed: false,
        };

        Ok(Artwork {
            id,
            title,
            artwork_type: ArtworkType::Novel,
            description,
            create_date,
            page_count: 1,
            width: 0,
            height: 0,
            sanity_level: 0,
            x_restrict: json["xRestrict"].as_u64().unwrap_or(0) as u32,
            tags,
            user,
            image_urls: Vec::new(),
            original_image_urls: Vec::new(),
        })
    }

    /// 获取多页作品的所有页面 URL
    async fn get_artwork_pages(&self, artwork_id: u64, page: u32) -> Result<(Vec<String>, Vec<String>), ApiError> {
        self.apply_delay().await;

        let url = format!("https://www.pixiv.net/ajax/illust/{}/pages", artwork_id);
        let response = self.make_request(&url).await?;
        let json: serde_json::Value = response.json().await?;

        if json["error"].as_bool().unwrap_or(true) {
            return Err(ApiError::Other(format!("获取作品 {} 页面失败", artwork_id)));
        }

        let mut image_urls = Vec::new();
        let mut original_urls = Vec::new();

        if let Some(pages) = json["body"].as_array() {
            for page_data in pages {
                if let Some(urls) = page_data["urls"].as_object() {
                    if let Some(thumb) = urls["thumb_mini"].as_str() {
                        image_urls.push(thumb.to_string());
                    }
                    if let Some(original) = urls["original"].as_str() {
                        original_urls.push(original.to_string());
                    }
                }
            }
        }

        Ok((image_urls, original_urls))
    }

    /// 获取小说内容
    pub async fn get_novel_content(&self, novel_id: u64) -> Result<String, ApiError> {
        self.apply_delay().await;

        let url = format!("https://www.pixiv.net/ajax/novel/{}", novel_id);
        let response = self.make_request(&url).await?;
        let json: serde_json::Value = response.json().await?;

        if json["error"].as_bool().unwrap_or(true) {
            return Err(ApiError::Other(format!("获取小说 {} 内容失败", novel_id)));
        }

        let content = json["body"]["content"].as_str().unwrap_or("").to_string();
        Ok(content)
    }

    /// 提取所有作品的唯一标签
    pub fn extract_unique_tags(&self, artworks: &[Artwork]) -> Vec<String> {
        let mut tags = std::collections::HashSet::new();

        for artwork in artworks {
            for tag in &artwork.tags {
                tags.insert(tag.clone());
            }
        }

        let mut sorted_tags: Vec<String> = tags.into_iter().collect();
        sorted_tags.sort();
        sorted_tags
    }
}
}
