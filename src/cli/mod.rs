/*!
 * CLI 模式模块
 * 
 * 实现命令行接口，支持非交互式自动化任务执行
 */

use anyhow::Result;
use log::{info, error, warn};
use std::path::PathBuf;
use tokio::sync::mpsc;

use crate::{
    config::Config,
    api::{PixivClient, ArtworkType},
    downloader::{DownloadManager, DownloadTask},
    storage::StorageManager,
    ArtworkFilter, AppError,
};

/// CLI 参数类型（从 main.rs 重新导入）
pub use crate::Args;

/// CLI 模式运行器
pub async fn run(config: Config, args: Args) -> Result<(), AppError> {
    info!("CLI 模式启动");
    
    // 验证必需的参数
    let uid = args.uid.ok_or_else(|| AppError::Other("CLI 模式需要指定 --uid 参数".to_string()))?;
    
    // 验证认证
    if !config.verify_auth().await? {
        return Err(AppError::Other("Cookie 认证失败，请检查配置文件中的 cookie 设置".to_string()));
    }
    
    // 创建 API 客户端
    let client = PixivClient::new(&config)?;
    
    // 获取用户信息
    info!("获取用户信息: UID {}", uid);
    let user_info = client.get_user_info(uid).await?;
    info!("用户: {} ({})", user_info.name, user_info.account);
    
    // 获取用户作品
    info!("获取用户作品列表...");
    let artworks = client.get_user_artworks(uid).await?;
    info!("获取到 {} 个作品", artworks.len());
    
    if artworks.is_empty() {
        warn!("用户没有公开作品");
        return Ok(());
    }
    
    // 构建筛选条件
    let mut filter = ArtworkFilter::new();
    
    // 设置标签筛选
    if let Some(tags) = args.tags {
        filter.tags = tags;
        filter.tag_logic = args.tag_logic.parse()?;
        info!("标签筛选: {:?} (逻辑: {:?})", filter.tags, filter.tag_logic);
    }
    
    // 设置作品类型筛选
    if let Some(types) = args.types {
        filter.artwork_types = types.into_iter()
            .filter_map(|t| match t.to_lowercase().as_str() {
                "illust" | "插画" => Some(ArtworkType::Illust),
                "manga" | "漫画" => Some(ArtworkType::Manga),
                "novel" | "小说" => Some(ArtworkType::Novel),
                _ => {
                    warn!("未知的作品类型: {}", t);
                    None
                }
            })
            .collect();
        info!("作品类型筛选: {:?}", filter.artwork_types);
    }
    
    // 应用筛选条件
    let filtered_artworks: Vec<_> = artworks.into_iter()
        .filter(|artwork| filter.matches(artwork))
        .collect();
        
    info!("筛选后的作品数量: {}", filtered_artworks.len());
    
    if filtered_artworks.is_empty() {
        warn!("没有符合筛选条件的作品");
        return Ok(());
    }
    
    // 创建存储管理器
    let output_dir = args.output_dir.map(PathBuf::from);
    let storage_manager = StorageManager::new(config.storage.clone(), output_dir);
    
    // 创建下载管理器
    let (progress_tx, mut progress_rx) = mpsc::unbounded_channel();
    let download_manager = DownloadManager::new(&config, progress_tx)?;
    
    // 生成下载任务
    let mut download_tasks = Vec::new();
    
    for artwork in &filtered_artworks {
        match artwork.artwork_type {
            ArtworkType::Novel => {
                // 小说下载
                let novel_path = storage_manager.generate_artwork_path(artwork, None, "txt")?;
                let novel_path = storage_manager.handle_file_conflict(&novel_path).await?;
                
                // 获取小说内容
                let content = client.get_novel_content(artwork.id).await?;
                let full_content = crate::storage::MetadataGenerator::generate_novel_content(artwork, &content);
                
                // 确保目录存在
                storage_manager.ensure_directory(&novel_path).await?;
                
                // 保存小说文件
                tokio::fs::write(&novel_path, full_content).await?;
                info!("保存小说: {}", novel_path.display());
            }
            ArtworkType::Illust | ArtworkType::Manga => {
                // 图片下载
                for (page_index, url) in artwork.original_image_urls.iter().enumerate() {
                    let extension = extract_extension_from_url(url).unwrap_or("jpg");
                    let page_idx = if artwork.page_count > 1 { Some(page_index as u32) } else { None };
                    
                    let image_path = storage_manager.generate_artwork_path(artwork, page_idx, extension)?;
                    let image_path = storage_manager.handle_file_conflict(&image_path).await?;
                    
                    let filename = image_path.file_name()
                        .and_then(|n| n.to_str())
                        .unwrap_or("unknown")
                        .to_string();
                        
                    let task = DownloadTask::new(
                        url.clone(),
                        image_path,
                        filename,
                        artwork.clone(),
                        page_idx,
                    );
                    
                    download_tasks.push(task);
                }
                
                // 保存元数据
                if let Some(first_task) = download_tasks.last() {
                    storage_manager.save_metadata(artwork, &first_task.output_path).await?;
                }
            }
        }
    }
    
    if download_tasks.is_empty() {
        info!("没有需要下载的文件");
        return Ok(());
    }
    
    info!("开始下载 {} 个文件", download_tasks.len());
    
    // 启动进度监控任务
    let progress_handle = tokio::spawn(async move {
        let mut completed = 0;
        let mut failed = 0;
        let total = download_tasks.len();
        
        while let Some(progress) = progress_rx.recv().await {
            match progress.status {
                crate::downloader::DownloadStatus::Completed => {
                    completed += 1;
                    info!("✓ {} ({}/{})", progress.filename, completed, total);
                }
                crate::downloader::DownloadStatus::Failed => {
                    failed += 1;
                    error!("✗ {} - {}", progress.filename, 
                        progress.error_message.unwrap_or_else(|| "未知错误".to_string()));
                }
                crate::downloader::DownloadStatus::Downloading => {
                    if let Some(percentage) = progress.progress_percentage() {
                        print!("\r⬇ {} - {:.1}%", progress.filename, percentage);
                        use std::io::{self, Write};
                        io::stdout().flush().unwrap();
                    }
                }
                _ => {}
            }
            
            if completed + failed >= total {
                break;
            }
        }
        
        println!("\n下载完成: 成功 {}, 失败 {}", completed, failed);
    });
    
    // 执行下载
    download_manager.download_batch(download_tasks).await?;
    
    // 等待进度监控完成
    progress_handle.await.map_err(|e| AppError::Other(format!("进度监控任务失败: {}", e)))?;
    
    info!("CLI 模式执行完成");
    Ok(())
}

/// 从 URL 中提取文件扩展名
fn extract_extension_from_url(url: &str) -> Option<&str> {
    url.split('?')
        .next()?
        .split('.')
        .last()
        .filter(|ext| !ext.is_empty())
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_extract_extension_from_url() {
        assert_eq!(
            extract_extension_from_url("https://example.com/image.jpg"),
            Some("jpg")
        );
        assert_eq!(
            extract_extension_from_url("https://example.com/image.png?param=value"),
            Some("png")
        );
        assert_eq!(
            extract_extension_from_url("https://example.com/image"),
            None
        );
    }
}
