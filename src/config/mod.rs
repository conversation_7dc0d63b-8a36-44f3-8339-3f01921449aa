/*!
 * 配置系统模块
 * 
 * 负责处理 config.yaml 配置文件的加载、验证和管理
 */

use anyhow::Result;
use serde::{Deserialize, Serialize};
use std::path::Path;
use tokio::fs;
use log::{info, warn, error};

/// 配置错误类型
#[derive(thiserror::Error, Debug)]
pub enum ConfigError {
    #[error("配置文件不存在: {0}")]
    FileNotFound(String),
    
    #[error("配置文件格式错误: {0}")]
    InvalidFormat(#[from] serde_yaml::Error),
    
    #[error("认证配置无效: {0}")]
    InvalidAuth(String),
    
    #[error("IO 错误: {0}")]
    Io(#[from] std::io::Error),
    
    #[error("其他配置错误: {0}")]
    Other(String),
}

/// 主配置结构
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct Config {
    /// 认证配置
    pub auth: AuthConfig,
    
    /// 下载配置
    pub download: DownloadConfig,
    
    /// 存储配置
    pub storage: StorageConfig,
    
    /// 日志配置
    pub logging: LoggingConfig,
}

/// 认证配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AuthConfig {
    /// Pixiv Cookie
    pub cookie: String,
    
    /// 用户代理
    #[serde(default = "default_user_agent")]
    pub user_agent: String,
    
    /// 请求超时时间（秒）
    #[serde(default = "default_timeout")]
    pub timeout: u64,
}

/// 下载配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DownloadConfig {
    /// 最大并发下载数
    #[serde(default = "default_max_concurrent")]
    pub max_concurrent: usize,
    
    /// 请求间随机延迟范围（毫秒）
    #[serde(default = "default_delay_range")]
    pub delay_range: (u64, u64),
    
    /// 最大重试次数
    #[serde(default = "default_max_retries")]
    pub max_retries: usize,
    
    /// 重试延迟（毫秒）
    #[serde(default = "default_retry_delay")]
    pub retry_delay: u64,
    
    /// 是否启用断点续传
    #[serde(default = "default_resume")]
    pub enable_resume: bool,
}

/// 存储配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StorageConfig {
    /// 输出根目录
    #[serde(default = "default_output_dir")]
    pub output_dir: String,
    
    /// 插画存储配置
    pub illust: ArtworkStorageConfig,
    
    /// 漫画存储配置
    pub manga: ArtworkStorageConfig,
    
    /// 小说存储配置
    pub novel: NovelStorageConfig,
    
    /// 文件冲突处理策略
    #[serde(default = "default_conflict_strategy")]
    pub conflict_strategy: ConflictStrategy,
    
    /// 是否生成元数据文件
    #[serde(default = "default_generate_metadata")]
    pub generate_metadata: bool,
}

/// 作品存储配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ArtworkStorageConfig {
    /// 目录路径模板
    pub directory_template: String,
    
    /// 文件名模板
    pub filename_template: String,
    
    /// 元数据文件名模板
    #[serde(default = "default_metadata_template")]
    pub metadata_template: String,
}

/// 小说存储配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NovelStorageConfig {
    /// 目录路径模板
    pub directory_template: String,
    
    /// 文件名模板
    pub filename_template: String,
    
    /// 是否在文件头部包含元数据
    #[serde(default = "default_include_metadata")]
    pub include_metadata_header: bool,
}

/// 文件冲突处理策略
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "lowercase")]
pub enum ConflictStrategy {
    Skip,
    Overwrite,
    Rename,
}

/// 日志配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LoggingConfig {
    /// 日志级别
    #[serde(default = "default_log_level")]
    pub level: String,
    
    /// 是否输出到文件
    #[serde(default = "default_log_to_file")]
    pub log_to_file: bool,
    
    /// 日志文件路径
    #[serde(default = "default_log_file")]
    pub log_file: String,
}

// 默认值函数
fn default_user_agent() -> String {
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36".to_string()
}

fn default_timeout() -> u64 { 30 }
fn default_max_concurrent() -> usize { 3 }
fn default_delay_range() -> (u64, u64) { (1000, 3000) }
fn default_max_retries() -> usize { 3 }
fn default_retry_delay() -> u64 { 5000 }
fn default_resume() -> bool { true }
fn default_output_dir() -> String { "./downloads".to_string() }
fn default_conflict_strategy() -> ConflictStrategy { ConflictStrategy::Skip }
fn default_generate_metadata() -> bool { true }
fn default_metadata_template() -> String { "metadata.txt".to_string() }
fn default_include_metadata() -> bool { true }
fn default_log_level() -> String { "info".to_string() }
fn default_log_to_file() -> bool { false }
fn default_log_file() -> String { "pixiv_downloader.log".to_string() }

impl Config {
    /// 加载配置文件
    pub async fn load<P: AsRef<Path>>(path: P) -> Result<Self, ConfigError> {
        let path = path.as_ref();
        
        if !path.exists() {
            info!("配置文件不存在，创建示例配置文件...");
            Self::create_example_config(path).await?;
            return Err(ConfigError::FileNotFound(format!(
                "配置文件 {} 不存在，已创建示例文件 {}.example，请根据示例配置后重新运行",
                path.display(),
                path.display()
            )));
        }
        
        let content = fs::read_to_string(path).await?;
        let config: Config = serde_yaml::from_str(&content)?;
        
        // 验证配置
        config.validate()?;
        
        info!("配置文件加载成功: {}", path.display());
        Ok(config)
    }
    
    /// 创建示例配置文件
    async fn create_example_config<P: AsRef<Path>>(path: P) -> Result<(), ConfigError> {
        let example_path = format!("{}.example", path.as_ref().display());
        let example_config = Self::default_config();
        let content = serde_yaml::to_string(&example_config)?;
        
        let content_with_comments = Self::add_comments_to_config(content);
        fs::write(&example_path, content_with_comments).await?;
        
        info!("示例配置文件已创建: {}", example_path);
        Ok(())
    }
    
    /// 验证配置
    fn validate(&self) -> Result<(), ConfigError> {
        if self.auth.cookie.trim().is_empty() {
            return Err(ConfigError::InvalidAuth(
                "Cookie 不能为空，请在配置文件中设置有效的 Pixiv Cookie".to_string()
            ));
        }
        
        if self.download.max_concurrent == 0 {
            return Err(ConfigError::Other("最大并发数不能为 0".to_string()));
        }
        
        Ok(())
    }
    
    /// 生成默认配置
    fn default_config() -> Self {
        Self {
            auth: AuthConfig {
                cookie: "".to_string(),
                user_agent: default_user_agent(),
                timeout: default_timeout(),
            },
            download: DownloadConfig {
                max_concurrent: default_max_concurrent(),
                delay_range: default_delay_range(),
                max_retries: default_max_retries(),
                retry_delay: default_retry_delay(),
                enable_resume: default_resume(),
            },
            storage: StorageConfig {
                output_dir: default_output_dir(),
                illust: ArtworkStorageConfig {
                    directory_template: "{username}({uid})/插画".to_string(),
                    filename_template: "{title}({pid})".to_string(),
                    metadata_template: default_metadata_template(),
                },
                manga: ArtworkStorageConfig {
                    directory_template: "{username}({uid})/漫画/{title}({pid})".to_string(),
                    filename_template: "{title}({pid})_p{page_indexzfill3}".to_string(),
                    metadata_template: default_metadata_template(),
                },
                novel: NovelStorageConfig {
                    directory_template: "{username}({uid})/小说".to_string(),
                    filename_template: "{title}({pid}).txt".to_string(),
                    include_metadata_header: default_include_metadata(),
                },
                conflict_strategy: default_conflict_strategy(),
                generate_metadata: default_generate_metadata(),
            },
            logging: LoggingConfig {
                level: default_log_level(),
                log_to_file: default_log_to_file(),
                log_file: default_log_file(),
            },
        }
    }
    
    /// 为配置文件添加中文注释
    fn add_comments_to_config(content: String) -> String {
        let commented_content = content
            .lines()
            .map(|line| {
                if line.starts_with("auth:") {
                    format!("{}\n# 认证配置 - 必须填写有效的 Pixiv Cookie", line)
                } else if line.contains("cookie:") {
                    format!("{} # 从浏览器获取的 Pixiv Cookie", line)
                } else if line.starts_with("download:") {
                    format!("{}\n# 下载配置", line)
                } else if line.contains("max_concurrent:") {
                    format!("{} # 最大并发下载数", line)
                } else if line.contains("delay_range:") {
                    format!("{} # 请求间随机延迟范围（毫秒）", line)
                } else if line.starts_with("storage:") {
                    format!("{}\n# 存储配置", line)
                } else if line.contains("directory_template:") {
                    format!("{} # 目录路径模板", line)
                } else if line.contains("filename_template:") {
                    format!("{} # 文件名模板", line)
                } else {
                    line.to_string()
                }
            })
            .collect::<Vec<_>>()
            .join("\n");

        format!(
            "# Pixiv Tag Downloader 配置文件\n\
             # 请根据需要修改以下配置项\n\
             # 注意：cookie 字段必须填写有效的 Pixiv Cookie\n\
             # \n\
             # 模板变量说明：\n\
             # {{uid}} - 用户ID\n\
             # {{username}} - 用户名\n\
             # {{pid}} - 作品ID\n\
             # {{title}} - 作品标题\n\
             # {{page_index}} - 页面索引（从0开始）\n\
             # {{page_indexzfill3}} - 页面索引（3位数字，前导零）\n\
             # {{date}} - 发布日期\n\
             # {{tags}} - 标签列表\n\n{}",
            commented_content
        )
    }

    /// 验证 Cookie 有效性
    pub async fn verify_auth(&self) -> Result<bool, ConfigError> {
        use reqwest::Client;

        let client = Client::builder()
            .timeout(std::time::Duration::from_secs(self.auth.timeout))
            .user_agent(&self.auth.user_agent)
            .build()
            .map_err(|e| ConfigError::Other(format!("创建 HTTP 客户端失败: {}", e)))?;

        // 尝试访问需要认证的 API 端点
        let response = client
            .get("https://www.pixiv.net/ajax/user/extra")
            .header("Cookie", &self.auth.cookie)
            .header("Referer", "https://www.pixiv.net/")
            .send()
            .await
            .map_err(|e| ConfigError::Other(format!("认证验证请求失败: {}", e)))?;

        if response.status().is_success() {
            info!("Cookie 认证验证成功");
            Ok(true)
        } else {
            warn!("Cookie 认证验证失败，状态码: {}", response.status());
            Ok(false)
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::TempDir;
    use tokio::fs;

    fn create_test_config() -> Config {
        Config {
            auth: AuthConfig {
                cookie: "test_cookie".to_string(),
                user_agent: default_user_agent(),
                timeout: default_timeout(),
            },
            download: DownloadConfig {
                max_concurrent: default_max_concurrent(),
                delay_range: default_delay_range(),
                max_retries: default_max_retries(),
                retry_delay: default_retry_delay(),
                enable_resume: default_resume(),
            },
            storage: StorageConfig {
                output_dir: default_output_dir(),
                illust: ArtworkStorageConfig {
                    directory_template: "{username}({uid})/插画".to_string(),
                    filename_template: "{title}({pid})".to_string(),
                    metadata_template: default_metadata_template(),
                },
                manga: ArtworkStorageConfig {
                    directory_template: "{username}({uid})/漫画/{title}({pid})".to_string(),
                    filename_template: "{title}({pid})_p{page_indexzfill3}".to_string(),
                    metadata_template: default_metadata_template(),
                },
                novel: NovelStorageConfig {
                    directory_template: "{username}({uid})/小说".to_string(),
                    filename_template: "{title}({pid}).txt".to_string(),
                    include_metadata_header: default_include_metadata(),
                },
                conflict_strategy: default_conflict_strategy(),
                generate_metadata: default_generate_metadata(),
            },
            logging: LoggingConfig {
                level: default_log_level(),
                log_to_file: default_log_to_file(),
                log_file: default_log_file(),
            },
        }
    }

    #[tokio::test]
    async fn test_config_validation() {
        let mut config = create_test_config();

        // 测试有效配置
        assert!(config.validate().is_ok());

        // 测试无效配置 - 空 Cookie
        config.auth.cookie = "".to_string();
        assert!(config.validate().is_err());

        // 测试无效配置 - 零并发数
        config.auth.cookie = "test_cookie".to_string();
        config.download.max_concurrent = 0;
        assert!(config.validate().is_err());
    }

    #[tokio::test]
    async fn test_config_file_creation() {
        let temp_dir = TempDir::new().unwrap();
        let config_path = temp_dir.path().join("test_config.yaml");

        // 测试配置文件不存在时的行为
        let result = Config::load(&config_path).await;
        assert!(result.is_err());

        // 验证示例配置文件被创建
        let example_path = format!("{}.example", config_path.display());
        assert!(std::path::Path::new(&example_path).exists());
    }

    #[tokio::test]
    async fn test_config_serialization() {
        let config = create_test_config();

        // 测试序列化
        let yaml_content = serde_yaml::to_string(&config).unwrap();
        assert!(yaml_content.contains("cookie: test_cookie"));

        // 测试反序列化
        let deserialized: Config = serde_yaml::from_str(&yaml_content).unwrap();
        assert_eq!(deserialized.auth.cookie, "test_cookie");
        assert_eq!(deserialized.download.max_concurrent, 3);
    }

    #[test]
    fn test_conflict_strategy_serialization() {
        assert_eq!(
            serde_yaml::to_string(&ConflictStrategy::Skip).unwrap().trim(),
            "skip"
        );
        assert_eq!(
            serde_yaml::to_string(&ConflictStrategy::Overwrite).unwrap().trim(),
            "overwrite"
        );
        assert_eq!(
            serde_yaml::to_string(&ConflictStrategy::Rename).unwrap().trim(),
            "rename"
        );
    }

    #[test]
    fn test_default_values() {
        assert_eq!(default_max_concurrent(), 3);
        assert_eq!(default_delay_range(), (1000, 3000));
        assert_eq!(default_max_retries(), 3);
        assert_eq!(default_retry_delay(), 5000);
        assert_eq!(default_resume(), true);
        assert_eq!(default_conflict_strategy(), ConflictStrategy::Skip);
        assert_eq!(default_generate_metadata(), true);
    }
}
