/*!
 * 下载引擎模块
 * 
 * 实现异步并发下载，支持重试机制、断点续传和进度跟踪
 */

use anyhow::Result;
use futures::stream::{self, StreamExt};
use reqwest::Client;
use serde::{Deserialize, Serialize};
use std::path::{Path, PathBuf};
use std::sync::Arc;
use tokio::fs::{File, OpenOptions};
use tokio::io::{AsyncSeekExt, AsyncWriteExt, SeekFrom};
use tokio::sync::{mpsc, Mutex, RwLock};
use tokio::time::{sleep, Duration};
use log::{debug, info, warn, error};
use rand::Rng;

use crate::config::{Config, DownloadConfig};
use crate::api::{Artwork, PixivClient};

/// 下载错误类型
#[derive(thiserror::Error, Debug)]
pub enum DownloadError {
    #[error("网络错误: {0}")]
    Network(#[from] reqwest::Error),
    
    #[error("文件 IO 错误: {0}")]
    Io(#[from] std::io::Error),
    
    #[error("下载任务创建失败: {0}")]
    TaskCreation(String),
    
    #[error("下载超时: {0}")]
    Timeout(String),
    
    #[error("下载被取消")]
    Cancelled,
    
    #[error("其他下载错误: {0}")]
    Other(String),
}

/// 下载状态
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum DownloadStatus {
    Pending,
    Downloading,
    Completed,
    Failed,
    Cancelled,
    Paused,
}

impl std::fmt::Display for DownloadStatus {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            DownloadStatus::Pending => write!(f, "等待中"),
            DownloadStatus::Downloading => write!(f, "下载中"),
            DownloadStatus::Completed => write!(f, "已完成"),
            DownloadStatus::Failed => write!(f, "失败"),
            DownloadStatus::Cancelled => write!(f, "已取消"),
            DownloadStatus::Paused => write!(f, "已暂停"),
        }
    }
}

/// 下载进度信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DownloadProgress {
    pub task_id: String,
    pub filename: String,
    pub downloaded_bytes: u64,
    pub total_bytes: Option<u64>,
    pub status: DownloadStatus,
    pub speed_bps: f64,
    pub eta_seconds: Option<u64>,
    pub error_message: Option<String>,
}

impl DownloadProgress {
    pub fn new(task_id: String, filename: String) -> Self {
        Self {
            task_id,
            filename,
            downloaded_bytes: 0,
            total_bytes: None,
            status: DownloadStatus::Pending,
            speed_bps: 0.0,
            eta_seconds: None,
            error_message: None,
        }
    }
    
    pub fn progress_percentage(&self) -> Option<f64> {
        self.total_bytes.map(|total| {
            if total == 0 {
                100.0
            } else {
                (self.downloaded_bytes as f64 / total as f64) * 100.0
            }
        })
    }
}

/// 下载任务
#[derive(Debug, Clone)]
pub struct DownloadTask {
    pub id: String,
    pub url: String,
    pub output_path: PathBuf,
    pub filename: String,
    pub referer: String,
    pub artwork: Artwork,
    pub page_index: Option<u32>,
}

impl DownloadTask {
    pub fn new(
        url: String,
        output_path: PathBuf,
        filename: String,
        artwork: Artwork,
        page_index: Option<u32>,
    ) -> Self {
        let id = uuid::Uuid::new_v4().to_string();
        Self {
            id,
            url,
            output_path,
            filename,
            referer: "https://www.pixiv.net/".to_string(),
            artwork,
            page_index,
        }
    }
}

/// 下载管理器
pub struct DownloadManager {
    client: Client,
    config: DownloadConfig,
    progress_sender: mpsc::UnboundedSender<DownloadProgress>,
    active_tasks: Arc<RwLock<std::collections::HashMap<String, DownloadTask>>>,
    cancelled_tasks: Arc<RwLock<std::collections::HashSet<String>>>,
}

impl DownloadManager {
    /// 创建新的下载管理器
    pub fn new(
        config: &Config,
        progress_sender: mpsc::UnboundedSender<DownloadProgress>,
    ) -> Result<Self, DownloadError> {
        let client = Client::builder()
            .timeout(Duration::from_secs(config.auth.timeout))
            .user_agent(&config.auth.user_agent)
            .build()
            .map_err(DownloadError::Network)?;
            
        Ok(Self {
            client,
            config: config.download.clone(),
            progress_sender,
            active_tasks: Arc::new(RwLock::new(std::collections::HashMap::new())),
            cancelled_tasks: Arc::new(RwLock::new(std::collections::HashSet::new())),
        })
    }
    
    /// 批量下载任务
    pub async fn download_batch(&self, tasks: Vec<DownloadTask>) -> Result<(), DownloadError> {
        info!("开始批量下载，任务数量: {}", tasks.len());
        
        // 将任务添加到活动任务列表
        {
            let mut active_tasks = self.active_tasks.write().await;
            for task in &tasks {
                active_tasks.insert(task.id.clone(), task.clone());
            }
        }
        
        // 使用信号量控制并发数
        let semaphore = Arc::new(tokio::sync::Semaphore::new(self.config.max_concurrent));
        
        // 创建任务流
        let task_stream = stream::iter(tasks)
            .map(|task| {
                let semaphore = semaphore.clone();
                let manager = self.clone();
                
                async move {
                    let _permit = semaphore.acquire().await.unwrap();
                    manager.download_single_task(task).await
                }
            })
            .buffer_unordered(self.config.max_concurrent);
            
        // 执行所有任务
        let results: Vec<Result<(), DownloadError>> = task_stream.collect().await;
        
        // 统计结果
        let mut success_count = 0;
        let mut failed_count = 0;
        
        for result in results {
            match result {
                Ok(_) => success_count += 1,
                Err(e) => {
                    failed_count += 1;
                    error!("下载任务失败: {}", e);
                }
            }
        }
        
        info!("批量下载完成，成功: {}, 失败: {}", success_count, failed_count);
        Ok(())
    }
    
    /// 下载单个任务
    async fn download_single_task(&self, task: DownloadTask) -> Result<(), DownloadError> {
        let task_id = task.id.clone();
        
        // 检查任务是否被取消
        if self.is_task_cancelled(&task_id).await {
            return Err(DownloadError::Cancelled);
        }
        
        info!("开始下载: {}", task.filename);
        
        // 创建进度跟踪
        let mut progress = DownloadProgress::new(task_id.clone(), task.filename.clone());
        
        // 确保输出目录存在
        if let Some(parent) = task.output_path.parent() {
            tokio::fs::create_dir_all(parent).await?;
        }
        
        // 检查是否支持断点续传
        let mut start_pos = 0u64;
        if self.config.enable_resume && task.output_path.exists() {
            start_pos = tokio::fs::metadata(&task.output_path).await?.len();
            progress.downloaded_bytes = start_pos;
            info!("检测到部分下载文件，从位置 {} 继续下载", start_pos);
        }
        
        // 执行下载（带重试）
        for attempt in 1..=self.config.max_retries {
            match self.download_with_resume(&task, &mut progress, start_pos).await {
                Ok(_) => {
                    progress.status = DownloadStatus::Completed;
                    self.send_progress(progress).await;
                    info!("下载完成: {}", task.filename);
                    
                    // 从活动任务中移除
                    self.active_tasks.write().await.remove(&task_id);
                    return Ok(());
                }
                Err(e) => {
                    warn!("下载尝试 {}/{} 失败: {}", attempt, self.config.max_retries, e);
                    
                    if attempt < self.config.max_retries {
                        progress.status = DownloadStatus::Pending;
                        progress.error_message = Some(format!("重试 {}/{}", attempt, self.config.max_retries));
                        self.send_progress(progress.clone()).await;
                        
                        // 重试延迟
                        sleep(Duration::from_millis(self.config.retry_delay)).await;
                    } else {
                        progress.status = DownloadStatus::Failed;
                        progress.error_message = Some(e.to_string());
                        self.send_progress(progress).await;
                        
                        // 从活动任务中移除
                        self.active_tasks.write().await.remove(&task_id);
                        return Err(e);
                    }
                }
            }
        }
        
        Err(DownloadError::Other("所有重试均失败".to_string()))
    }
    
    /// 执行带断点续传的下载
    async fn download_with_resume(
        &self,
        task: &DownloadTask,
        progress: &mut DownloadProgress,
        start_pos: u64,
    ) -> Result<(), DownloadError> {
        progress.status = DownloadStatus::Downloading;
        self.send_progress(progress.clone()).await;
        
        // 应用随机延迟
        self.apply_delay().await;
        
        // 构建请求
        let mut request = self.client
            .get(&task.url)
            .header("Referer", &task.referer);
            
        // 如果支持断点续传且有起始位置，添加 Range 头
        if start_pos > 0 {
            request = request.header("Range", format!("bytes={}-", start_pos));
        }
        
        let response = request.send().await?;
        
        // 检查响应状态
        if !response.status().is_success() && response.status() != reqwest::StatusCode::PARTIAL_CONTENT {
            return Err(DownloadError::Other(format!(
                "HTTP 错误: {}", 
                response.status()
            )));
        }
        
        // 获取文件总大小
        let content_length = response.content_length();
        if let Some(total) = content_length {
            progress.total_bytes = Some(start_pos + total);
        }
        
        // 打开文件进行写入
        let mut file = if start_pos > 0 {
            let mut file = OpenOptions::new()
                .write(true)
                .append(true)
                .open(&task.output_path)
                .await?;
            file.seek(SeekFrom::End(0)).await?;
            file
        } else {
            File::create(&task.output_path).await?
        };
        
        // 下载数据
        let mut stream = response.bytes_stream();
        let mut last_update = std::time::Instant::now();
        let mut bytes_since_last_update = 0u64;
        
        while let Some(chunk) = stream.next().await {
            // 检查任务是否被取消
            if self.is_task_cancelled(&task.id).await {
                return Err(DownloadError::Cancelled);
            }
            
            let chunk = chunk?;
            file.write_all(&chunk).await?;
            
            progress.downloaded_bytes += chunk.len() as u64;
            bytes_since_last_update += chunk.len() as u64;
            
            // 每秒更新一次进度
            let now = std::time::Instant::now();
            if now.duration_since(last_update).as_secs() >= 1 {
                let elapsed = now.duration_since(last_update).as_secs_f64();
                progress.speed_bps = bytes_since_last_update as f64 / elapsed;
                
                // 计算预计剩余时间
                if let Some(total) = progress.total_bytes {
                    let remaining = total.saturating_sub(progress.downloaded_bytes);
                    if progress.speed_bps > 0.0 {
                        progress.eta_seconds = Some((remaining as f64 / progress.speed_bps) as u64);
                    }
                }
                
                self.send_progress(progress.clone()).await;
                
                last_update = now;
                bytes_since_last_update = 0;
            }
        }
        
        file.flush().await?;
        Ok(())
    }
    
    /// 取消任务
    pub async fn cancel_task(&self, task_id: &str) {
        self.cancelled_tasks.write().await.insert(task_id.to_string());
        info!("任务已标记为取消: {}", task_id);
    }
    
    /// 检查任务是否被取消
    async fn is_task_cancelled(&self, task_id: &str) -> bool {
        self.cancelled_tasks.read().await.contains(task_id)
    }
    
    /// 发送进度更新
    async fn send_progress(&self, progress: DownloadProgress) {
        if let Err(e) = self.progress_sender.send(progress) {
            error!("发送进度更新失败: {}", e);
        }
    }
    
    /// 应用随机延迟
    async fn apply_delay(&self) {
        let (min, max) = self.config.delay_range;
        let delay = rand::thread_rng().gen_range(min..=max);
        debug!("应用下载延迟: {}ms", delay);
        sleep(Duration::from_millis(delay)).await;
    }
}

impl Clone for DownloadManager {
    fn clone(&self) -> Self {
        Self {
            client: self.client.clone(),
            config: self.config.clone(),
            progress_sender: self.progress_sender.clone(),
            active_tasks: self.active_tasks.clone(),
            cancelled_tasks: self.cancelled_tasks.clone(),
        }
    }
}
