/*!
 * GUI 模式模块
 * 
 * 使用 Slint 框架实现图形用户界面
 */

use anyhow::Result;
use log::{info, error, warn};
use slint::{ComponentHandle, Model, ModelRc, VecModel};
use std::collections::HashMap;
use std::path::PathBuf;
use std::sync::Arc;
use tokio::sync::{mpsc, Mutex, RwLock};

use crate::{
    config::Config,
    api::{PixivClient, ArtworkType},
    downloader::{DownloadManager, DownloadTask, DownloadProgress, DownloadStatus},
    storage::StorageManager,
    ArtworkFilter, TagLogic, AppError,
};

// 包含生成的 Slint 代码
slint::include_modules!();

/// GUI 应用状态
#[derive(Clone)]
struct AppState {
    config: Arc<RwLock<Config>>,
    client: Option<Arc<PixivClient>>,
    download_manager: Option<Arc<DownloadManager>>,
    storage_manager: Option<Arc<StorageManager>>,
    current_artworks: Arc<RwLock<Vec<crate::api::Artwork>>>,
    active_downloads: Arc<RwLock<HashMap<String, DownloadTask>>>,
}

impl AppState {
    fn new(config: Config) -> Self {
        Self {
            config: Arc::new(RwLock::new(config)),
            client: None,
            download_manager: None,
            storage_manager: None,
            current_artworks: Arc::new(RwLock::new(Vec::new())),
            active_downloads: Arc::new(RwLock::new(HashMap::new())),
        }
    }
}

/// GUI 模式运行器
pub async fn run(config: Config, _args: crate::Args) -> Result<(), AppError> {
    info!("GUI 模式启动");
    
    // 创建应用状态
    let app_state = AppState::new(config.clone());
    
    // 创建主窗口
    let main_window = MainWindow::new()
        .map_err(|e| AppError::Other(format!("创建主窗口失败: {}", e)))?;
    
    // 设置窗口回调
    setup_main_window_callbacks(&main_window, app_state.clone()).await?;
    
    // 初始化 API 客户端
    let client = PixivClient::new(&config)?;
    let mut state = app_state.clone();
    state.client = Some(Arc::new(client));
    
    // 创建存储管理器
    let storage_manager = StorageManager::new(config.storage.clone(), None);
    state.storage_manager = Some(Arc::new(storage_manager));
    
    // 显示窗口
    main_window.show()
        .map_err(|e| AppError::Other(format!("显示窗口失败: {}", e)))?;
    
    // 运行事件循环
    slint::run_event_loop()
        .map_err(|e| AppError::Other(format!("事件循环失败: {}", e)))?;
    
    info!("GUI 模式退出");
    Ok(())
}

/// 设置主窗口回调函数
async fn setup_main_window_callbacks(
    window: &MainWindow,
    app_state: AppState,
) -> Result<(), AppError> {
    // 获取用户信息回调
    {
        let window_weak = window.as_weak();
        let state = app_state.clone();
        
        window.on_fetch_user_info(move |uid_str| {
            let window_weak = window_weak.clone();
            let state = state.clone();
            
            slint::spawn_local(async move {
                if let Some(window) = window_weak.upgrade() {
                    fetch_user_info_impl(window, state, uid_str.to_string()).await;
                }
            }).unwrap();
        });
    }
    
    // 开始下载回调
    {
        let window_weak = window.as_weak();
        let state = app_state.clone();
        
        window.on_start_download(move || {
            let window_weak = window_weak.clone();
            let state = state.clone();
            
            slint::spawn_local(async move {
                if let Some(window) = window_weak.upgrade() {
                    start_download_impl(window, state).await;
                }
            }).unwrap();
        });
    }
    
    // 取消下载回调
    {
        let window_weak = window.as_weak();
        let state = app_state.clone();
        
        window.on_cancel_download(move || {
            let window_weak = window_weak.clone();
            let state = state.clone();
            
            slint::spawn_local(async move {
                if let Some(window) = window_weak.upgrade() {
                    cancel_download_impl(window, state).await;
                }
            }).unwrap();
        });
    }
    
    // 标签选择变化回调
    {
        let window_weak = window.as_weak();
        
        window.on_tag_selection_changed(move |index, selected| {
            if let Some(window) = window_weak.upgrade() {
                let mut tags = window.get_tags();
                if let Some(tag_item) = tags.row_data(index as usize) {
                    let mut new_tag = tag_item.unwrap();
                    new_tag.selected = selected;
                    tags.set_row_data(index as usize, new_tag);
                    window.set_tags(tags);
                }
            }
        });
    }
    
    // 打开设置回调
    {
        let window_weak = window.as_weak();
        let state = app_state.clone();
        
        window.on_open_settings(move || {
            let window_weak = window_weak.clone();
            let state = state.clone();
            
            slint::spawn_local(async move {
                if let Some(_window) = window_weak.upgrade() {
                    open_settings_impl(state).await;
                }
            }).unwrap();
        });
    }
    
    Ok(())
}

/// 获取用户信息实现
async fn fetch_user_info_impl(window: MainWindow, state: AppState, uid_str: String) {
    window.set_is_fetching(true);
    window.set_status_message("正在获取用户信息...".into());
    
    let uid = match uid_str.parse::<u64>() {
        Ok(uid) => uid,
        Err(_) => {
            window.set_status_message("无效的 UID".into());
            window.set_is_fetching(false);
            return;
        }
    };
    
    if let Some(client) = &state.client {
        match client.get_user_info(uid).await {
            Ok(user_info) => {
                window.set_username(user_info.name.into());
                window.set_status_message("用户信息获取成功".into());
                
                // 获取用户作品
                window.set_status_message("正在获取作品列表...".into());
                match client.get_user_artworks(uid).await {
                    Ok(artworks) => {
                        // 更新作品列表
                        *state.current_artworks.write().await = artworks.clone();
                        
                        // 提取标签
                        let all_tags = client.extract_unique_tags(&artworks);
                        let tag_items: Vec<TagItem> = all_tags.into_iter()
                            .map(|tag| TagItem {
                                name: tag.into(),
                                selected: false,
                            })
                            .collect();
                        
                        let tags_model = ModelRc::new(VecModel::from(tag_items));
                        window.set_tags(tags_model);
                        
                        window.set_status_message(format!("获取到 {} 个作品", artworks.len()).into());
                    }
                    Err(e) => {
                        error!("获取作品列表失败: {}", e);
                        window.set_status_message("获取作品列表失败".into());
                    }
                }
            }
            Err(e) => {
                error!("获取用户信息失败: {}", e);
                window.set_status_message("获取用户信息失败".into());
            }
        }
    }
    
    window.set_is_fetching(false);
}

/// 开始下载实现
async fn start_download_impl(window: MainWindow, state: AppState) {
    window.set_is_downloading(true);
    window.set_status_message("准备下载...".into());
    
    // 获取选中的标签
    let selected_tags: Vec<String> = window.get_tags()
        .iter()
        .filter_map(|item| {
            if item.selected {
                Some(item.name.to_string())
            } else {
                None
            }
        })
        .collect();
    
    // 构建筛选条件
    let tag_logic = if window.get_tag_logic().as_str() == "and" {
        TagLogic::And
    } else {
        TagLogic::Or
    };
    
    let mut artwork_types = Vec::new();
    if window.get_illust_enabled() {
        artwork_types.push(ArtworkType::Illust);
    }
    if window.get_manga_enabled() {
        artwork_types.push(ArtworkType::Manga);
    }
    if window.get_novel_enabled() {
        artwork_types.push(ArtworkType::Novel);
    }
    
    let filter = ArtworkFilter {
        tags: selected_tags,
        tag_logic,
        artwork_types,
    };
    
    // 应用筛选
    let artworks = state.current_artworks.read().await;
    let filtered_artworks: Vec<_> = artworks.iter()
        .filter(|artwork| filter.matches(artwork))
        .cloned()
        .collect();
    
    if filtered_artworks.is_empty() {
        window.set_status_message("没有符合条件的作品".into());
        window.set_is_downloading(false);
        return;
    }
    
    // 创建下载任务
    let (progress_tx, mut progress_rx) = mpsc::unbounded_channel();
    
    // 创建下载管理器
    let config = state.config.read().await;
    let download_manager = match DownloadManager::new(&config, progress_tx) {
        Ok(manager) => Arc::new(manager),
        Err(e) => {
            error!("创建下载管理器失败: {}", e);
            window.set_status_message("创建下载管理器失败".into());
            window.set_is_downloading(false);
            return;
        }
    };
    
    let storage_manager = state.storage_manager.as_ref().unwrap().clone();
    
    // 生成下载任务
    let mut download_tasks = Vec::new();
    
    for artwork in &filtered_artworks {
        match artwork.artwork_type {
            ArtworkType::Novel => {
                // 小说处理
                if let Some(client) = &state.client {
                    match client.get_novel_content(artwork.id).await {
                        Ok(content) => {
                            let novel_path = storage_manager.generate_artwork_path(artwork, None, "txt").unwrap();
                            let novel_path = storage_manager.handle_file_conflict(&novel_path).await.unwrap();
                            
                            let full_content = crate::storage::MetadataGenerator::generate_novel_content(artwork, &content);
                            
                            storage_manager.ensure_directory(&novel_path).await.unwrap();
                            tokio::fs::write(&novel_path, full_content).await.unwrap();
                        }
                        Err(e) => {
                            error!("获取小说内容失败: {}", e);
                        }
                    }
                }
            }
            ArtworkType::Illust | ArtworkType::Manga => {
                // 图片下载
                for (page_index, url) in artwork.original_image_urls.iter().enumerate() {
                    let extension = extract_extension_from_url(url).unwrap_or("jpg");
                    let page_idx = if artwork.page_count > 1 { Some(page_index as u32) } else { None };
                    
                    let image_path = storage_manager.generate_artwork_path(artwork, page_idx, extension).unwrap();
                    let image_path = storage_manager.handle_file_conflict(&image_path).await.unwrap();
                    
                    let filename = image_path.file_name()
                        .and_then(|n| n.to_str())
                        .unwrap_or("unknown")
                        .to_string();
                        
                    let task = DownloadTask::new(
                        url.clone(),
                        image_path,
                        filename,
                        artwork.clone(),
                        page_idx,
                    );
                    
                    download_tasks.push(task);
                }
            }
        }
    }
    
    let total_tasks = download_tasks.len();
    window.set_status_message(format!("开始下载 {} 个文件", total_tasks).into());
    
    // 启动进度监控
    let window_weak = window.as_weak();
    let progress_handle = tokio::spawn(async move {
        let mut download_items = Vec::new();
        let mut completed = 0;
        
        while let Some(progress) = progress_rx.recv().await {
            if let Some(window) = window_weak.upgrade() {
                // 更新下载项目
                let download_item = DownloadItem {
                    filename: progress.filename.into(),
                    status: progress.status.to_string().into(),
                    progress: progress.progress_percentage().unwrap_or(0.0) as f32 / 100.0,
                    size: format_bytes(progress.downloaded_bytes).into(),
                };
                
                // 查找或添加下载项目
                if let Some(existing) = download_items.iter_mut().find(|item| item.filename == download_item.filename) {
                    *existing = download_item;
                } else {
                    download_items.push(download_item);
                }
                
                // 更新界面
                let items_model = ModelRc::new(VecModel::from(download_items.clone()));
                window.set_download_items(items_model);
                
                // 更新总体进度
                if progress.status == DownloadStatus::Completed {
                    completed += 1;
                }
                
                let overall_progress = completed as f32 / total_tasks as f32;
                window.set_overall_progress(overall_progress);
                
                if completed >= total_tasks {
                    window.set_status_message("下载完成".into());
                    window.set_is_downloading(false);
                    break;
                }
            }
        }
    });
    
    // 执行下载
    tokio::spawn(async move {
        if let Err(e) = download_manager.download_batch(download_tasks).await {
            error!("下载失败: {}", e);
        }
    });
}

/// 取消下载实现
async fn cancel_download_impl(window: MainWindow, _state: AppState) {
    window.set_is_downloading(false);
    window.set_status_message("下载已取消".into());
}

/// 打开设置实现
async fn open_settings_impl(_state: AppState) {
    // 创建设置窗口
    if let Ok(settings_window) = SettingsWindow::new() {
        settings_window.show().unwrap();
    }
}

/// 从 URL 中提取文件扩展名
fn extract_extension_from_url(url: &str) -> Option<&str> {
    url.split('?')
        .next()?
        .split('.')
        .last()
        .filter(|ext| !ext.is_empty())
}

/// 格式化字节数
fn format_bytes(bytes: u64) -> String {
    const UNITS: &[&str] = &["B", "KB", "MB", "GB"];
    let mut size = bytes as f64;
    let mut unit_index = 0;
    
    while size >= 1024.0 && unit_index < UNITS.len() - 1 {
        size /= 1024.0;
        unit_index += 1;
    }
    
    format!("{:.1} {}", size, UNITS[unit_index])
}
