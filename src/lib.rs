/*!
 * Pixiv Tag Downloader 库模块
 * 
 * 提供核心功能模块的公共接口
 */

pub mod config;
pub mod api;
pub mod downloader;
pub mod storage;
pub mod gui;
pub mod tui;
pub mod cli;

// 重新导出核心类型
pub use config::{Config, AuthConfig, DownloadConfig, StorageConfig};
pub use api::{PixivClient, UserInfo, Artwork, ArtworkType};
pub use downloader::{DownloadManager, DownloadTask, DownloadProgress};
pub use storage::{StorageManager, FileTemplate, MetadataGenerator};

/// 应用程序错误类型
#[derive(thiserror::Error, Debug)]
pub enum AppError {
    #[error("配置错误: {0}")]
    Config(#[from] config::ConfigError),
    
    #[error("API 错误: {0}")]
    Api(#[from] api::ApiError),
    
    #[error("下载错误: {0}")]
    Download(#[from] downloader::DownloadError),
    
    #[error("存储错误: {0}")]
    Storage(#[from] storage::StorageError),
    
    #[error("IO 错误: {0}")]
    Io(#[from] std::io::Error),
    
    #[error("网络错误: {0}")]
    Network(#[from] reqwest::Error),
    
    #[error("序列化错误: {0}")]
    Serialization(#[from] serde_yaml::Error),
    
    #[error("其他错误: {0}")]
    Other(String),
}

/// 应用程序结果类型
pub type AppResult<T> = Result<T, AppError>;

/// 标签筛选逻辑
#[derive(Debug, Clone, PartialEq)]
pub enum TagLogic {
    And,
    Or,
}

impl std::str::FromStr for TagLogic {
    type Err = AppError;
    
    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s.to_lowercase().as_str() {
            "and" | "与" => Ok(TagLogic::And),
            "or" | "或" => Ok(TagLogic::Or),
            _ => Err(AppError::Other(format!("无效的标签逻辑: {}", s))),
        }
    }
}

/// 作品筛选条件
#[derive(Debug, Clone)]
pub struct ArtworkFilter {
    pub tags: Vec<String>,
    pub tag_logic: TagLogic,
    pub artwork_types: Vec<ArtworkType>,
}

impl ArtworkFilter {
    pub fn new() -> Self {
        Self {
            tags: Vec::new(),
            tag_logic: TagLogic::And,
            artwork_types: vec![ArtworkType::Illust, ArtworkType::Manga, ArtworkType::Novel],
        }
    }
    
    /// 检查作品是否匹配筛选条件
    pub fn matches(&self, artwork: &Artwork) -> bool {
        // 检查作品类型
        if !self.artwork_types.contains(&artwork.artwork_type) {
            return false;
        }
        
        // 如果没有标签筛选，则匹配
        if self.tags.is_empty() {
            return true;
        }
        
        // 检查标签匹配
        match self.tag_logic {
            TagLogic::And => {
                self.tags.iter().all(|tag| artwork.tags.contains(tag))
            }
            TagLogic::Or => {
                self.tags.iter().any(|tag| artwork.tags.contains(tag))
            }
        }
    }
}

impl Default for ArtworkFilter {
    fn default() -> Self {
        Self::new()
    }
}
