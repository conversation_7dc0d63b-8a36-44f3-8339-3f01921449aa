/*!
 * Pixiv Tag Downloader - 高效可配置的 Pixiv 作品下载工具
 *
 * 本程序根据用户指定的 Pixiv 用户ID (UID) 下载其发布的各类作品
 * 支持三种操作模式：GUI、TUI、CLI
 */

use anyhow::Result;
use clap::Parser;
use log::{error, info};

mod config;
mod api;
mod downloader;
mod storage;
mod gui;
mod tui;
mod cli;

// 重新导出 lib 模块的类型
use PixivTagDownloader::{ArtworkFilter, TagLogic};

use config::Config;

/// Pixiv Tag Downloader 命令行参数
#[derive(Parser, Debug)]
#[command(name = "PixivTagDownloader")]
#[command(about = "高效可配置的 Pixiv 作品下载工具")]
#[command(version)]
struct Args {
    /// 启动 GUI 模式
    #[arg(long)]
    gui: bool,

    /// 配置文件路径
    #[arg(short, long, value_name = "PATH")]
    config: Option<String>,

    /// 输出目录
    #[arg(short, long, value_name = "DIR")]
    output_dir: Option<String>,

    /// 目标用户 UID (CLI 模式)
    #[arg(short, long)]
    uid: Option<u64>,

    /// 标签筛选 (CLI 模式)
    #[arg(long, value_delimiter = ',')]
    tags: Option<Vec<String>>,

    /// 标签筛选逻辑: and 或 or (CLI 模式)
    #[arg(long, default_value = "and")]
    tag_logic: String,

    /// 作品类型筛选 (CLI 模式)
    #[arg(long, value_delimiter = ',')]
    types: Option<Vec<String>>,

    /// 日志级别
    #[arg(long, default_value = "info")]
    log_level: String,
}

#[tokio::main]
async fn main() -> Result<()> {
    let args = Args::parse();

    // 初始化日志系统
    init_logger(&args.log_level)?;

    info!("Pixiv Tag Downloader 启动中...");

    // 加载配置
    let config_path = args.config.as_deref().unwrap_or("config.yaml");
    let config = match Config::load(config_path).await {
        Ok(config) => config,
        Err(e) => {
            error!("配置加载失败: {}", e);
            return Err(e);
        }
    };

    // 根据参数决定运行模式
    if args.gui {
        eprintln!("GUI 模式暂时不可用，请使用 TUI 或 CLI 模式");
        std::process::exit(1);
    } else if args.uid.is_some() {
        info!("启动 CLI 模式");
        cli::run(config, args).await
    } else {
        info!("启动 TUI 模式");
        tui::run(config, args).await
    }
}

/// 初始化日志系统
fn init_logger(level: &str) -> Result<()> {
    let log_level = match level.to_lowercase().as_str() {
        "trace" => log::LevelFilter::Trace,
        "debug" => log::LevelFilter::Debug,
        "info" => log::LevelFilter::Info,
        "warn" => log::LevelFilter::Warn,
        "error" => log::LevelFilter::Error,
        _ => log::LevelFilter::Info,
    };

    env_logger::Builder::from_default_env()
        .filter_level(log_level)
        .format_timestamp_secs()
        .init();

    Ok(())
}
