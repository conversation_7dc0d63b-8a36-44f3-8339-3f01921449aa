/*!
 * 存储系统模块
 * 
 * 负责文件存储和组织，包括模板化命名、元数据生成、文件冲突处理
 */

use anyhow::Result;
use chrono::{DateTime, Utc};
use regex::Regex;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::path::{Path, PathBuf};
use tokio::fs;
use log::{debug, info, warn, error};

use crate::api::{Artwork, ArtworkType, UserInfo};
use crate::config::{StorageConfig, ConflictStrategy, ArtworkStorageConfig, NovelStorageConfig};

/// 存储错误类型
#[derive(thiserror::Error, Debug)]
pub enum StorageError {
    #[error("文件 IO 错误: {0}")]
    Io(#[from] std::io::Error),
    
    #[error("模板解析错误: {0}")]
    TemplateError(String),
    
    #[error("路径创建失败: {0}")]
    PathCreation(String),
    
    #[error("文件冲突: {0}")]
    FileConflict(String),
    
    #[error("其他存储错误: {0}")]
    Other(String),
}

/// 文件模板变量
#[derive(Debug, Clone)]
pub struct TemplateVariables {
    pub uid: u64,
    pub username: String,
    pub pid: u64,
    pub title: String,
    pub page_index: Option<u32>,
    pub page_indexzfill3: Option<String>,
    pub date: String,
    pub tags: String,
    pub artwork_type: String,
    pub extension: String,
}

impl TemplateVariables {
    pub fn from_artwork(artwork: &Artwork, page_index: Option<u32>, extension: &str) -> Self {
        let page_indexzfill3 = page_index.map(|i| format!("{:03}", i));
        
        Self {
            uid: artwork.user.user_id,
            username: sanitize_filename(&artwork.user.name),
            pid: artwork.id,
            title: sanitize_filename(&artwork.title),
            page_index,
            page_indexzfill3,
            date: artwork.create_date.clone(),
            tags: artwork.tags.join(","),
            artwork_type: artwork.artwork_type.to_string(),
            extension: extension.to_string(),
        }
    }
}

/// 文件模板处理器
pub struct FileTemplate {
    template: String,
    variables: TemplateVariables,
}

impl FileTemplate {
    pub fn new(template: String, variables: TemplateVariables) -> Self {
        Self { template, variables }
    }
    
    /// 渲染模板
    pub fn render(&self) -> Result<String, StorageError> {
        let mut result = self.template.clone();
        
        // 替换所有模板变量
        result = result.replace("{uid}", &self.variables.uid.to_string());
        result = result.replace("{username}", &self.variables.username);
        result = result.replace("{pid}", &self.variables.pid.to_string());
        result = result.replace("{title}", &self.variables.title);
        result = result.replace("{date}", &self.variables.date);
        result = result.replace("{tags}", &self.variables.tags);
        result = result.replace("{artwork_type}", &self.variables.artwork_type);
        result = result.replace("{extension}", &self.variables.extension);
        
        if let Some(page_index) = self.variables.page_index {
            result = result.replace("{page_index}", &page_index.to_string());
        }
        
        if let Some(ref page_indexzfill3) = self.variables.page_indexzfill3 {
            result = result.replace("{page_indexzfill3}", page_indexzfill3);
        }
        
        // 清理路径中的非法字符
        result = sanitize_path(&result);
        
        Ok(result)
    }
}

/// 元数据生成器
pub struct MetadataGenerator;

impl MetadataGenerator {
    /// 生成作品元数据
    pub fn generate_artwork_metadata(artwork: &Artwork) -> String {
        let mut metadata = String::new();
        
        metadata.push_str(&format!("作品ID: {}\n", artwork.id));
        metadata.push_str(&format!("标题: {}\n", artwork.title));
        metadata.push_str(&format!("作者: {} ({})\n", artwork.user.name, artwork.user.user_id));
        metadata.push_str(&format!("类型: {}\n", artwork.artwork_type));
        metadata.push_str(&format!("发布时间: {}\n", artwork.create_date));
        metadata.push_str(&format!("页数: {}\n", artwork.page_count));
        metadata.push_str(&format!("尺寸: {}x{}\n", artwork.width, artwork.height));
        metadata.push_str(&format!("标签: {}\n", artwork.tags.join(", ")));
        
        if !artwork.description.is_empty() {
            metadata.push_str(&format!("描述: {}\n", artwork.description));
        }
        
        metadata.push_str(&format!("下载时间: {}\n", Utc::now().format("%Y-%m-%d %H:%M:%S UTC")));
        metadata.push_str(&format!("原始链接: https://www.pixiv.net/artworks/{}\n", artwork.id));
        
        metadata
    }
    
    /// 生成小说元数据和内容
    pub fn generate_novel_content(artwork: &Artwork, content: &str) -> String {
        let mut result = String::new();
        
        // 添加元数据头部
        result.push_str("=" .repeat(50).as_str());
        result.push_str("\n");
        result.push_str(&format!("小说标题: {}\n", artwork.title));
        result.push_str(&format!("作者: {} ({})\n", artwork.user.name, artwork.user.user_id));
        result.push_str(&format!("发布时间: {}\n", artwork.create_date));
        result.push_str(&format!("标签: {}\n", artwork.tags.join(", ")));
        
        if !artwork.description.is_empty() {
            result.push_str(&format!("简介: {}\n", artwork.description));
        }
        
        result.push_str(&format!("原始链接: https://www.pixiv.net/novel/show.php?id={}\n", artwork.id));
        result.push_str(&format!("下载时间: {}\n", Utc::now().format("%Y-%m-%d %H:%M:%S UTC")));
        result.push_str("=" .repeat(50).as_str());
        result.push_str("\n\n");
        
        // 添加正文内容
        result.push_str(content);
        
        result
    }
}

/// 存储管理器
pub struct StorageManager {
    config: StorageConfig,
    base_path: PathBuf,
}

impl StorageManager {
    /// 创建新的存储管理器
    pub fn new(config: StorageConfig, base_path: Option<PathBuf>) -> Self {
        let base_path = base_path.unwrap_or_else(|| PathBuf::from(&config.output_dir));
        
        Self {
            config,
            base_path,
        }
    }
    
    /// 为作品生成存储路径
    pub fn generate_artwork_path(
        &self,
        artwork: &Artwork,
        page_index: Option<u32>,
        extension: &str,
    ) -> Result<PathBuf, StorageError> {
        let storage_config = match artwork.artwork_type {
            ArtworkType::Illust => &self.config.illust,
            ArtworkType::Manga => &self.config.manga,
            ArtworkType::Novel => {
                // 对于小说，我们需要特殊处理
                return self.generate_novel_path(artwork);
            }
        };
        
        let variables = TemplateVariables::from_artwork(artwork, page_index, extension);
        
        // 生成目录路径
        let dir_template = FileTemplate::new(storage_config.directory_template.clone(), variables.clone());
        let dir_path = dir_template.render()?;
        
        // 生成文件名
        let filename_template = FileTemplate::new(storage_config.filename_template.clone(), variables);
        let filename = filename_template.render()?;
        
        let full_path = self.base_path.join(dir_path).join(format!("{}.{}", filename, extension));
        Ok(full_path)
    }
    
    /// 为小说生成存储路径
    fn generate_novel_path(&self, artwork: &Artwork) -> Result<PathBuf, StorageError> {
        let variables = TemplateVariables::from_artwork(artwork, None, "txt");
        
        // 生成目录路径
        let dir_template = FileTemplate::new(self.config.novel.directory_template.clone(), variables.clone());
        let dir_path = dir_template.render()?;
        
        // 生成文件名
        let filename_template = FileTemplate::new(self.config.novel.filename_template.clone(), variables);
        let filename = filename_template.render()?;
        
        let full_path = self.base_path.join(dir_path).join(filename);
        Ok(full_path)
    }
    
    /// 生成元数据文件路径
    pub fn generate_metadata_path(&self, artwork: &Artwork, artwork_path: &Path) -> Result<PathBuf, StorageError> {
        match artwork.artwork_type {
            ArtworkType::Novel => {
                // 小说的元数据包含在文件内容中
                Ok(artwork_path.to_path_buf())
            }
            ArtworkType::Illust => {
                // 单页插画：在图片文件旁创建同名的 .txt 文件
                let mut metadata_path = artwork_path.to_path_buf();
                metadata_path.set_extension("txt");
                Ok(metadata_path)
            }
            ArtworkType::Manga => {
                // 多页漫画：在作品目录内创建单一的元数据文件
                let parent_dir = artwork_path.parent()
                    .ok_or_else(|| StorageError::PathCreation("无法获取父目录".to_string()))?;
                    
                let variables = TemplateVariables::from_artwork(artwork, None, "txt");
                let metadata_template = FileTemplate::new(
                    self.config.manga.metadata_template.clone(),
                    variables
                );
                let metadata_filename = metadata_template.render()?;
                
                Ok(parent_dir.join(metadata_filename))
            }
        }
    }
    
    /// 处理文件冲突
    pub async fn handle_file_conflict(&self, path: &Path) -> Result<PathBuf, StorageError> {
        if !path.exists() {
            return Ok(path.to_path_buf());
        }
        
        match self.config.conflict_strategy {
            ConflictStrategy::Skip => {
                info!("文件已存在，跳过: {}", path.display());
                Err(StorageError::FileConflict(format!("文件已存在: {}", path.display())))
            }
            ConflictStrategy::Overwrite => {
                warn!("文件已存在，将覆盖: {}", path.display());
                Ok(path.to_path_buf())
            }
            ConflictStrategy::Rename => {
                let new_path = self.generate_unique_filename(path).await?;
                info!("文件已存在，重命名为: {}", new_path.display());
                Ok(new_path)
            }
        }
    }
    
    /// 生成唯一文件名
    async fn generate_unique_filename(&self, original_path: &Path) -> Result<PathBuf, StorageError> {
        let parent = original_path.parent()
            .ok_or_else(|| StorageError::PathCreation("无法获取父目录".to_string()))?;
        let stem = original_path.file_stem()
            .and_then(|s| s.to_str())
            .ok_or_else(|| StorageError::PathCreation("无法获取文件名".to_string()))?;
        let extension = original_path.extension()
            .and_then(|s| s.to_str())
            .unwrap_or("");
            
        for i in 1..=9999 {
            let new_filename = if extension.is_empty() {
                format!("{}_{}", stem, i)
            } else {
                format!("{}_{}.{}", stem, i, extension)
            };
            
            let new_path = parent.join(new_filename);
            if !new_path.exists() {
                return Ok(new_path);
            }
        }
        
        Err(StorageError::Other("无法生成唯一文件名".to_string()))
    }
    
    /// 确保目录存在
    pub async fn ensure_directory(&self, path: &Path) -> Result<(), StorageError> {
        if let Some(parent) = path.parent() {
            fs::create_dir_all(parent).await?;
            debug!("创建目录: {}", parent.display());
        }
        Ok(())
    }
    
    /// 保存元数据文件
    pub async fn save_metadata(&self, artwork: &Artwork, artwork_path: &Path) -> Result<(), StorageError> {
        if !self.config.generate_metadata {
            return Ok(());
        }
        
        match artwork.artwork_type {
            ArtworkType::Novel => {
                // 小说的元数据已包含在内容中，无需单独保存
                Ok(())
            }
            _ => {
                let metadata_path = self.generate_metadata_path(artwork, artwork_path)?;
                let metadata_content = MetadataGenerator::generate_artwork_metadata(artwork);
                
                self.ensure_directory(&metadata_path).await?;
                fs::write(&metadata_path, metadata_content).await?;
                
                info!("保存元数据文件: {}", metadata_path.display());
                Ok(())
            }
        }
    }
}

/// 清理文件名中的非法字符
fn sanitize_filename(filename: &str) -> String {
    let illegal_chars = ['<', '>', ':', '"', '|', '?', '*', '/', '\\'];
    let mut result = filename.to_string();
    
    for &ch in &illegal_chars {
        result = result.replace(ch, "_");
    }
    
    // 移除前后空格和点
    result = result.trim().trim_matches('.').to_string();
    
    // 如果结果为空，使用默认名称
    if result.is_empty() {
        result = "untitled".to_string();
    }
    
    result
}

/// 清理路径中的非法字符
fn sanitize_path(path: &str) -> String {
    path.split(['/', '\\'])
        .map(sanitize_filename)
        .collect::<Vec<_>>()
        .join(std::path::MAIN_SEPARATOR_STR)
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::TempDir;
    use crate::api::{Artwork, ArtworkType, UserInfo};
    use std::collections::HashMap;

    fn create_test_artwork() -> Artwork {
        Artwork {
            id: 12345,
            title: "测试作品".to_string(),
            artwork_type: ArtworkType::Illust,
            description: "这是一个测试作品".to_string(),
            create_date: "2023-01-01T00:00:00Z".to_string(),
            page_count: 1,
            width: 1920,
            height: 1080,
            sanity_level: 2,
            x_restrict: 0,
            tags: vec!["测试".to_string(), "插画".to_string()],
            user: UserInfo {
                user_id: 67890,
                name: "测试用户".to_string(),
                account: "test_user".to_string(),
                profile_image_urls: HashMap::new(),
                comment: "测试用户简介".to_string(),
                is_followed: false,
            },
            image_urls: vec!["https://example.com/thumb.jpg".to_string()],
            original_image_urls: vec!["https://example.com/original.jpg".to_string()],
        }
    }

    fn create_test_storage_config() -> StorageConfig {
        StorageConfig {
            output_dir: "./test_downloads".to_string(),
            illust: ArtworkStorageConfig {
                directory_template: "{username}({uid})/插画".to_string(),
                filename_template: "{title}({pid})".to_string(),
                metadata_template: "metadata.txt".to_string(),
            },
            manga: ArtworkStorageConfig {
                directory_template: "{username}({uid})/漫画/{title}({pid})".to_string(),
                filename_template: "{title}({pid})_p{page_indexzfill3}".to_string(),
                metadata_template: "metadata.txt".to_string(),
            },
            novel: NovelStorageConfig {
                directory_template: "{username}({uid})/小说".to_string(),
                filename_template: "{title}({pid}).txt".to_string(),
                include_metadata_header: true,
            },
            conflict_strategy: ConflictStrategy::Skip,
            generate_metadata: true,
        }
    }

    #[test]
    fn test_template_variables_creation() {
        let artwork = create_test_artwork();
        let variables = TemplateVariables::from_artwork(&artwork, Some(0), "jpg");

        assert_eq!(variables.uid, 67890);
        assert_eq!(variables.username, "测试用户");
        assert_eq!(variables.pid, 12345);
        assert_eq!(variables.title, "测试作品");
        assert_eq!(variables.page_index, Some(0));
        assert_eq!(variables.page_indexzfill3, Some("000".to_string()));
        assert_eq!(variables.extension, "jpg");
    }

    #[test]
    fn test_template_rendering() {
        let artwork = create_test_artwork();
        let variables = TemplateVariables::from_artwork(&artwork, Some(1), "png");

        // 测试基本模板
        let template = FileTemplate::new("{title}({pid})".to_string(), variables.clone());
        let result = template.render().unwrap();
        assert_eq!(result, "测试作品(12345)");

        // 测试带页面索引的模板
        let template = FileTemplate::new("{title}_p{page_indexzfill3}".to_string(), variables.clone());
        let result = template.render().unwrap();
        assert_eq!(result, "测试作品_p001");

        // 测试复杂模板
        let template = FileTemplate::new("{username}({uid})/{title}({pid}).{extension}".to_string(), variables);
        let result = template.render().unwrap();
        assert_eq!(result, "测试用户(67890)/测试作品(12345).png");
    }

    #[test]
    fn test_sanitize_filename() {
        assert_eq!(sanitize_filename("normal_file"), "normal_file");
        assert_eq!(sanitize_filename("file<with>illegal:chars"), "file_with_illegal_chars");
        assert_eq!(sanitize_filename("file|with?more*illegal/chars\\"), "file_with_more_illegal_chars_");
        assert_eq!(sanitize_filename("  .file with spaces.  "), "file with spaces");
        assert_eq!(sanitize_filename(""), "untitled");
        assert_eq!(sanitize_filename("..."), "untitled");
    }

    #[test]
    fn test_sanitize_path() {
        assert_eq!(sanitize_path("normal/path"), "normal/path");
        assert_eq!(sanitize_path("path/with<illegal>chars"), "path/with_illegal_chars");
        assert_eq!(sanitize_path("path\\with\\backslashes"), "path/with/backslashes");
    }

    #[tokio::test]
    async fn test_storage_path_generation() {
        let temp_dir = TempDir::new().unwrap();
        let config = create_test_storage_config();
        let storage_manager = StorageManager::new(config, Some(temp_dir.path().to_path_buf()));

        let artwork = create_test_artwork();

        // 测试插画路径生成
        let illust_path = storage_manager.generate_artwork_path(&artwork, None, "jpg").unwrap();
        assert!(illust_path.to_string_lossy().contains("测试用户(67890)"));
        assert!(illust_path.to_string_lossy().contains("插画"));
        assert!(illust_path.to_string_lossy().contains("测试作品(12345).jpg"));

        // 测试漫画路径生成
        let mut manga_artwork = artwork.clone();
        manga_artwork.artwork_type = ArtworkType::Manga;
        manga_artwork.page_count = 3;

        let manga_path = storage_manager.generate_artwork_path(&manga_artwork, Some(1), "jpg").unwrap();
        assert!(manga_path.to_string_lossy().contains("漫画"));
        assert!(manga_path.to_string_lossy().contains("测试作品(12345)_p001.jpg"));
    }

    #[test]
    fn test_metadata_generation() {
        let artwork = create_test_artwork();

        // 测试作品元数据生成
        let metadata = MetadataGenerator::generate_artwork_metadata(&artwork);
        assert!(metadata.contains("作品ID: 12345"));
        assert!(metadata.contains("标题: 测试作品"));
        assert!(metadata.contains("作者: 测试用户 (67890)"));
        assert!(metadata.contains("标签: 测试, 插画"));
        assert!(metadata.contains("原始链接: https://www.pixiv.net/artworks/12345"));

        // 测试小说内容生成
        let novel_content = "这是小说的正文内容。\n第二段内容。";
        let full_content = MetadataGenerator::generate_novel_content(&artwork, novel_content);
        assert!(full_content.contains("小说标题: 测试作品"));
        assert!(full_content.contains("作者: 测试用户 (67890)"));
        assert!(full_content.contains("这是小说的正文内容。"));
        assert!(full_content.contains("第二段内容。"));
        assert!(full_content.contains("=" .repeat(50).as_str()));
    }

    #[tokio::test]
    async fn test_file_conflict_handling() {
        let temp_dir = TempDir::new().unwrap();
        let mut config = create_test_storage_config();
        config.conflict_strategy = ConflictStrategy::Rename;

        let storage_manager = StorageManager::new(config, Some(temp_dir.path().to_path_buf()));

        // 创建一个已存在的文件
        let existing_file = temp_dir.path().join("test.txt");
        tokio::fs::write(&existing_file, "existing content").await.unwrap();

        // 测试重命名策略
        let new_path = storage_manager.handle_file_conflict(&existing_file).await.unwrap();
        assert_ne!(new_path, existing_file);
        assert!(new_path.to_string_lossy().contains("test_1.txt"));

        // 测试跳过策略
        let mut skip_config = create_test_storage_config();
        skip_config.conflict_strategy = ConflictStrategy::Skip;
        let skip_manager = StorageManager::new(skip_config, Some(temp_dir.path().to_path_buf()));

        let result = skip_manager.handle_file_conflict(&existing_file).await;
        assert!(result.is_err());

        // 测试覆盖策略
        let mut overwrite_config = create_test_storage_config();
        overwrite_config.conflict_strategy = ConflictStrategy::Overwrite;
        let overwrite_manager = StorageManager::new(overwrite_config, Some(temp_dir.path().to_path_buf()));

        let overwrite_path = overwrite_manager.handle_file_conflict(&existing_file).await.unwrap();
        assert_eq!(overwrite_path, existing_file);
    }

    #[tokio::test]
    async fn test_directory_creation() {
        let temp_dir = TempDir::new().unwrap();
        let config = create_test_storage_config();
        let storage_manager = StorageManager::new(config, Some(temp_dir.path().to_path_buf()));

        let test_path = temp_dir.path().join("deep/nested/directory/file.txt");

        // 确保目录被创建
        storage_manager.ensure_directory(&test_path).await.unwrap();

        // 验证目录存在
        assert!(test_path.parent().unwrap().exists());
    }
}
