/*!
 * TUI 模式模块
 * 
 * 实现交互式终端界面，包括用户输入、标签选择、进度显示
 */

use anyhow::Result;
use console::style;
use dialoguer::{theme::ColorfulTheme, Confirm, Input, MultiSelect, Select};
use indicatif::{MultiProgress, ProgressBar, ProgressStyle};
use log::{info, error, warn};
use std::collections::HashMap;
use std::path::PathBuf;
use std::sync::Arc;
use tokio::sync::{mpsc, Mutex};

use crate::{
    config::Config,
    api::{PixivClient, ArtworkType},
    downloader::{DownloadManager, DownloadTask, DownloadStatus},
    storage::StorageManager,
    ArtworkFilter, TagLogic, AppError,
};

/// TUI 参数类型（从 main.rs 重新导入）
pub use crate::Args;

/// TUI 模式运行器
pub async fn run(config: Config, args: Args) -> Result<(), AppError> {
    println!("{}", style("=".repeat(60)).cyan());
    println!("{}", style("    Pixiv Tag Downloader - 交互式模式").cyan().bold());
    println!("{}", style("=".repeat(60)).cyan());
    println!();
    
    // 验证认证
    print!("正在验证认证信息...");
    if !config.verify_auth().await? {
        println!(" {}", style("失败").red());
        return Err(AppError::Other("Cookie 认证失败，请检查配置文件中的 cookie 设置".to_string()));
    }
    println!(" {}", style("成功").green());
    
    // 创建 API 客户端
    let client = PixivClient::new(&config)?;
    
    // 获取用户 UID
    let uid: u64 = Input::with_theme(&ColorfulTheme::default())
        .with_prompt("请输入目标用户的 Pixiv UID")
        .validate_with(|input: &String| -> Result<(), &str> {
            if input.parse::<u64>().is_ok() {
                Ok(())
            } else {
                Err("请输入有效的数字 UID")
            }
        })
        .interact_text()
        .map_err(|e| AppError::Other(format!("输入错误: {}", e)))?
        .parse()
        .unwrap();
    
    // 获取用户信息
    print!("正在获取用户信息...");
    let user_info = match client.get_user_info(uid).await {
        Ok(info) => {
            println!(" {}", style("成功").green());
            println!("用户: {} ({})", style(&info.name).yellow(), style(&info.account).dim());
            info
        }
        Err(e) => {
            println!(" {}", style("失败").red());
            return Err(AppError::Api(e));
        }
    };
    
    // 获取用户作品
    print!("正在获取作品列表...");
    let artworks = match client.get_user_artworks(uid).await {
        Ok(artworks) => {
            println!(" {}", style("成功").green());
            println!("获取到 {} 个作品", style(artworks.len()).yellow());
            artworks
        }
        Err(e) => {
            println!(" {}", style("失败").red());
            return Err(AppError::Api(e));
        }
    };
    
    if artworks.is_empty() {
        println!("{}", style("用户没有公开作品").yellow());
        return Ok(());
    }
    
    // 提取所有唯一标签
    let all_tags = client.extract_unique_tags(&artworks);
    println!("发现 {} 个唯一标签", style(all_tags.len()).yellow());
    
    // 标签选择
    let selected_tags = if all_tags.is_empty() {
        Vec::new()
    } else {
        let should_select_tags = Confirm::with_theme(&ColorfulTheme::default())
            .with_prompt("是否要按标签筛选作品？")
            .default(false)
            .interact()
            .map_err(|e| AppError::Other(format!("输入错误: {}", e)))?;
            
        if should_select_tags {
            let selections = MultiSelect::with_theme(&ColorfulTheme::default())
                .with_prompt("请选择要下载的标签（空格选择，回车确认）")
                .items(&all_tags)
                .interact()
                .map_err(|e| AppError::Other(format!("输入错误: {}", e)))?;
                
            selections.into_iter().map(|i| all_tags[i].clone()).collect()
        } else {
            Vec::new()
        }
    };
    
    // 标签筛选逻辑
    let tag_logic = if selected_tags.len() > 1 {
        let logic_options = vec!["与 (AND) - 必须包含所有选中的标签", "或 (OR) - 包含任意一个选中的标签"];
        let logic_selection = Select::with_theme(&ColorfulTheme::default())
            .with_prompt("请选择标签筛选逻辑")
            .items(&logic_options)
            .default(0)
            .interact()
            .map_err(|e| AppError::Other(format!("输入错误: {}", e)))?;
            
        if logic_selection == 0 { TagLogic::And } else { TagLogic::Or }
    } else {
        TagLogic::And
    };
    
    // 作品类型选择
    let type_options = vec!["插画 (Illust)", "漫画 (Manga)", "小说 (Novel)"];
    let type_selections = MultiSelect::with_theme(&ColorfulTheme::default())
        .with_prompt("请选择要下载的作品类型")
        .items(&type_options)
        .defaults(&[true, true, true])
        .interact()
        .map_err(|e| AppError::Other(format!("输入错误: {}", e)))?;
        
    let selected_types: Vec<ArtworkType> = type_selections.into_iter()
        .map(|i| match i {
            0 => ArtworkType::Illust,
            1 => ArtworkType::Manga,
            2 => ArtworkType::Novel,
            _ => unreachable!(),
        })
        .collect();
    
    // 构建筛选条件
    let filter = ArtworkFilter {
        tags: selected_tags.clone(),
        tag_logic: tag_logic.clone(),
        artwork_types: selected_types,
    };

    // 应用筛选条件
    let filtered_artworks: Vec<_> = artworks.into_iter()
        .filter(|artwork| filter.matches(artwork))
        .collect();

    println!();
    println!("筛选结果:");
    if !selected_tags.is_empty() {
        println!("  标签: {} ({:?})", selected_tags.join(", "), tag_logic);
    }
    println!("  作品类型: {}", filter.artwork_types.iter()
        .map(|t| t.to_string())
        .collect::<Vec<_>>()
        .join(", "));
    println!("  符合条件的作品: {}", style(filtered_artworks.len()).yellow());
    
    if filtered_artworks.is_empty() {
        println!("{}", style("没有符合筛选条件的作品").yellow());
        return Ok(());
    }
    
    // 确认下载
    let should_download = Confirm::with_theme(&ColorfulTheme::default())
        .with_prompt("是否开始下载？")
        .default(true)
        .interact()
        .map_err(|e| AppError::Other(format!("输入错误: {}", e)))?;
        
    if !should_download {
        println!("下载已取消");
        return Ok(());
    }
    
    // 创建存储管理器
    let output_dir = args.output_dir.map(PathBuf::from);
    let storage_manager = StorageManager::new(config.storage.clone(), output_dir);
    
    // 创建下载管理器
    let (progress_tx, progress_rx) = mpsc::unbounded_channel();
    let download_manager = DownloadManager::new(&config, progress_tx)?;
    
    // 生成下载任务
    let mut download_tasks = Vec::new();
    
    println!("\n正在准备下载任务...");
    
    for artwork in &filtered_artworks {
        match artwork.artwork_type {
            ArtworkType::Novel => {
                // 小说下载
                let novel_path = storage_manager.generate_artwork_path(artwork, None, "txt")?;
                let novel_path = storage_manager.handle_file_conflict(&novel_path).await?;
                
                // 获取小说内容
                let content = client.get_novel_content(artwork.id).await?;
                let full_content = crate::storage::MetadataGenerator::generate_novel_content(artwork, &content);
                
                // 确保目录存在
                storage_manager.ensure_directory(&novel_path).await?;
                
                // 保存小说文件
                tokio::fs::write(&novel_path, full_content).await?;
                println!("✓ 保存小说: {}", novel_path.display());
            }
            ArtworkType::Illust | ArtworkType::Manga => {
                // 图片下载
                for (page_index, url) in artwork.original_image_urls.iter().enumerate() {
                    let extension = extract_extension_from_url(url).unwrap_or("jpg");
                    let page_idx = if artwork.page_count > 1 { Some(page_index as u32) } else { None };
                    
                    let image_path = storage_manager.generate_artwork_path(artwork, page_idx, extension)?;
                    let image_path = storage_manager.handle_file_conflict(&image_path).await?;
                    
                    let filename = image_path.file_name()
                        .and_then(|n| n.to_str())
                        .unwrap_or("unknown")
                        .to_string();
                        
                    let task = DownloadTask::new(
                        url.clone(),
                        image_path,
                        filename,
                        artwork.clone(),
                        page_idx,
                    );
                    
                    download_tasks.push(task);
                }
                
                // 保存元数据
                if let Some(first_task) = download_tasks.last() {
                    storage_manager.save_metadata(artwork, &first_task.output_path).await?;
                }
            }
        }
    }
    
    if download_tasks.is_empty() {
        println!("没有需要下载的文件");
        return Ok(());
    }
    
    let total_tasks = download_tasks.len();
    println!("准备下载 {} 个文件", style(total_tasks).yellow());
    println!();

    // 创建进度条
    let multi_progress = Arc::new(MultiProgress::new());
    let overall_progress = multi_progress.add(ProgressBar::new(total_tasks as u64));
    overall_progress.set_style(
        ProgressStyle::default_bar()
            .template("{spinner:.green} [{elapsed_precise}] [{bar:40.cyan/blue}] {pos}/{len} ({eta})")
            .unwrap()
            .progress_chars("#>-")
    );
    overall_progress.set_message("总体进度");
    
    let progress_bars: Arc<Mutex<HashMap<String, ProgressBar>>> = Arc::new(Mutex::new(HashMap::new()));
    
    // 启动进度监控任务
    let progress_handle = {
        let multi_progress = multi_progress.clone();
        let overall_progress = overall_progress.clone();
        let progress_bars = progress_bars.clone();
        
        tokio::spawn(async move {
            let mut progress_rx = progress_rx;
            let mut completed = 0;
            let mut failed = 0;
            
            while let Some(progress) = progress_rx.recv().await {
                let mut bars = progress_bars.lock().await;
                
                match progress.status {
                    DownloadStatus::Downloading => {
                        if !bars.contains_key(&progress.task_id) {
                            let pb = multi_progress.add(ProgressBar::new(100));
                            pb.set_style(
                                ProgressStyle::default_bar()
                                    .template("  {spinner:.green} {msg} [{bar:30.cyan/blue}] {percent}%")
                                    .unwrap()
                                    .progress_chars("#>-")
                            );
                            pb.set_message(progress.filename.clone());
                            bars.insert(progress.task_id.clone(), pb);
                        }
                        
                        if let Some(pb) = bars.get(&progress.task_id) {
                            if let Some(percentage) = progress.progress_percentage() {
                                pb.set_position(percentage as u64);
                            }
                        }
                    }
                    DownloadStatus::Completed => {
                        if let Some(pb) = bars.remove(&progress.task_id) {
                            pb.finish_with_message(format!("✓ {}", progress.filename));
                        }
                        completed += 1;
                        overall_progress.inc(1);
                    }
                    DownloadStatus::Failed => {
                        if let Some(pb) = bars.remove(&progress.task_id) {
                            pb.finish_with_message(format!("✗ {}", progress.filename));
                        }
                        failed += 1;
                        overall_progress.inc(1);
                    }
                    _ => {}
                }
                
                if completed + failed >= total_tasks {
                    break;
                }
            }
            
            overall_progress.finish_with_message(format!("完成: 成功 {}, 失败 {}", completed, failed));
        })
    };
    
    // 执行下载
    download_manager.download_batch(download_tasks).await?;
    
    // 等待进度监控完成
    progress_handle.await.map_err(|e| AppError::Other(format!("进度监控任务失败: {}", e)))?;
    
    println!("\n{}", style("下载完成！").green().bold());
    Ok(())
}

/// 从 URL 中提取文件扩展名
fn extract_extension_from_url(url: &str) -> Option<&str> {
    url.split('?')
        .next()?
        .split('.')
        .last()
        .filter(|ext| !ext.is_empty())
}
