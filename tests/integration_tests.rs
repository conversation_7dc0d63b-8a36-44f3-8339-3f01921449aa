/*!
 * 集成测试
 * 
 * 验证应用程序的核心功能
 */

use std::path::PathBuf;
use tempfile::TempDir;
use tokio::fs;

use PixivTagDownloader::{
    config::{Config, AuthConfig, DownloadConfig, StorageConfig, ArtworkStorageConfig, NovelStorageConfig, LoggingConfig, ConflictStrategy},
    api::{Artwork, ArtworkType, UserInfo},
    storage::{StorageManager, FileTemplate, TemplateVariables, MetadataGenerator},
    ArtworkFilter, TagLogic,
};

/// 创建测试配置
fn create_test_config() -> Config {
    Config {
        auth: AuthConfig {
            cookie: "test_cookie".to_string(),
            user_agent: "test_agent".to_string(),
            timeout: 30,
        },
        download: DownloadConfig {
            max_concurrent: 2,
            delay_range: (100, 200),
            max_retries: 2,
            retry_delay: 1000,
            enable_resume: true,
        },
        storage: StorageConfig {
            output_dir: "./test_downloads".to_string(),
            illust: ArtworkStorageConfig {
                directory_template: "{username}({uid})/插画".to_string(),
                filename_template: "{title}({pid})".to_string(),
                metadata_template: "metadata.txt".to_string(),
            },
            manga: ArtworkStorageConfig {
                directory_template: "{username}({uid})/漫画/{title}({pid})".to_string(),
                filename_template: "{title}({pid})_p{page_indexzfill3}".to_string(),
                metadata_template: "metadata.txt".to_string(),
            },
            novel: NovelStorageConfig {
                directory_template: "{username}({uid})/小说".to_string(),
                filename_template: "{title}({pid}).txt".to_string(),
                include_metadata_header: true,
            },
            conflict_strategy: ConflictStrategy::Skip,
            generate_metadata: true,
        },
        logging: LoggingConfig {
            level: "info".to_string(),
            log_to_file: false,
            log_file: "test.log".to_string(),
        },
    }
}

/// 创建测试作品
fn create_test_artwork() -> Artwork {
    Artwork {
        id: 12345,
        title: "测试作品".to_string(),
        artwork_type: ArtworkType::Illust,
        description: "这是一个测试作品".to_string(),
        create_date: "2023-01-01T00:00:00Z".to_string(),
        page_count: 1,
        width: 1920,
        height: 1080,
        sanity_level: 2,
        x_restrict: 0,
        tags: vec!["测试".to_string(), "插画".to_string()],
        user: UserInfo {
            user_id: 67890,
            name: "测试用户".to_string(),
            account: "test_user".to_string(),
            profile_image_urls: std::collections::HashMap::new(),
            comment: "测试用户简介".to_string(),
            is_followed: false,
        },
        image_urls: vec!["https://example.com/thumb.jpg".to_string()],
        original_image_urls: vec!["https://example.com/original.jpg".to_string()],
    }
}

#[tokio::test]
async fn test_config_validation() {
    let mut config = create_test_config();
    
    // 测试有效配置
    assert!(config.validate().is_ok());
    
    // 测试无效配置 - 空 Cookie
    config.auth.cookie = "".to_string();
    assert!(config.validate().is_err());
    
    // 测试无效配置 - 零并发数
    config.auth.cookie = "test_cookie".to_string();
    config.download.max_concurrent = 0;
    assert!(config.validate().is_err());
}

#[tokio::test]
async fn test_template_rendering() {
    let artwork = create_test_artwork();
    let variables = TemplateVariables::from_artwork(&artwork, Some(0), "jpg");
    
    // 测试基本模板
    let template = FileTemplate::new("{title}({pid})".to_string(), variables.clone());
    let result = template.render().unwrap();
    assert_eq!(result, "测试作品(12345)");
    
    // 测试带页面索引的模板
    let template = FileTemplate::new("{title}_p{page_indexzfill3}".to_string(), variables.clone());
    let result = template.render().unwrap();
    assert_eq!(result, "测试作品_p000");
    
    // 测试复杂模板
    let template = FileTemplate::new("{username}({uid})/{title}({pid}).{extension}".to_string(), variables);
    let result = template.render().unwrap();
    assert_eq!(result, "测试用户(67890)/测试作品(12345).jpg");
}

#[tokio::test]
async fn test_storage_path_generation() {
    let temp_dir = TempDir::new().unwrap();
    let config = create_test_config();
    let storage_manager = StorageManager::new(config.storage, Some(temp_dir.path().to_path_buf()));
    
    let artwork = create_test_artwork();
    
    // 测试插画路径生成
    let illust_path = storage_manager.generate_artwork_path(&artwork, None, "jpg").unwrap();
    assert!(illust_path.to_string_lossy().contains("测试用户(67890)/插画"));
    assert!(illust_path.to_string_lossy().contains("测试作品(12345).jpg"));
    
    // 测试漫画路径生成
    let mut manga_artwork = artwork.clone();
    manga_artwork.artwork_type = ArtworkType::Manga;
    manga_artwork.page_count = 3;
    
    let manga_path = storage_manager.generate_artwork_path(&manga_artwork, Some(1), "jpg").unwrap();
    assert!(manga_path.to_string_lossy().contains("测试用户(67890)/漫画/测试作品(12345)"));
    assert!(manga_path.to_string_lossy().contains("测试作品(12345)_p001.jpg"));
    
    // 测试小说路径生成
    let mut novel_artwork = artwork.clone();
    novel_artwork.artwork_type = ArtworkType::Novel;
    
    let novel_path = storage_manager.generate_artwork_path(&novel_artwork, None, "txt").unwrap();
    assert!(novel_path.to_string_lossy().contains("测试用户(67890)/小说"));
    assert!(novel_path.to_string_lossy().contains("测试作品(12345).txt"));
}

#[tokio::test]
async fn test_metadata_generation() {
    let artwork = create_test_artwork();
    
    // 测试作品元数据生成
    let metadata = MetadataGenerator::generate_artwork_metadata(&artwork);
    assert!(metadata.contains("作品ID: 12345"));
    assert!(metadata.contains("标题: 测试作品"));
    assert!(metadata.contains("作者: 测试用户 (67890)"));
    assert!(metadata.contains("标签: 测试, 插画"));
    
    // 测试小说内容生成
    let novel_content = "这是小说的正文内容。";
    let full_content = MetadataGenerator::generate_novel_content(&artwork, novel_content);
    assert!(full_content.contains("小说标题: 测试作品"));
    assert!(full_content.contains("作者: 测试用户 (67890)"));
    assert!(full_content.contains("这是小说的正文内容。"));
}

#[tokio::test]
async fn test_artwork_filter() {
    let artworks = vec![
        {
            let mut artwork = create_test_artwork();
            artwork.id = 1;
            artwork.tags = vec!["风景".to_string(), "自然".to_string()];
            artwork.artwork_type = ArtworkType::Illust;
            artwork
        },
        {
            let mut artwork = create_test_artwork();
            artwork.id = 2;
            artwork.tags = vec!["人物".to_string(), "肖像".to_string()];
            artwork.artwork_type = ArtworkType::Manga;
            artwork
        },
        {
            let mut artwork = create_test_artwork();
            artwork.id = 3;
            artwork.tags = vec!["风景".to_string(), "城市".to_string()];
            artwork.artwork_type = ArtworkType::Novel;
            artwork
        },
    ];
    
    // 测试标签筛选 - AND 逻辑
    let filter = ArtworkFilter {
        tags: vec!["风景".to_string()],
        tag_logic: TagLogic::And,
        artwork_types: vec![ArtworkType::Illust, ArtworkType::Manga, ArtworkType::Novel],
    };
    
    let filtered: Vec<_> = artworks.iter().filter(|a| filter.matches(a)).collect();
    assert_eq!(filtered.len(), 2); // 作品 1 和 3
    
    // 测试作品类型筛选
    let filter = ArtworkFilter {
        tags: vec![],
        tag_logic: TagLogic::And,
        artwork_types: vec![ArtworkType::Illust],
    };
    
    let filtered: Vec<_> = artworks.iter().filter(|a| filter.matches(a)).collect();
    assert_eq!(filtered.len(), 1); // 只有作品 1
    
    // 测试复合筛选
    let filter = ArtworkFilter {
        tags: vec!["风景".to_string()],
        tag_logic: TagLogic::And,
        artwork_types: vec![ArtworkType::Illust],
    };
    
    let filtered: Vec<_> = artworks.iter().filter(|a| filter.matches(a)).collect();
    assert_eq!(filtered.len(), 1); // 只有作品 1
}

#[tokio::test]
async fn test_file_conflict_handling() {
    let temp_dir = TempDir::new().unwrap();
    let mut config = create_test_config();
    config.storage.conflict_strategy = ConflictStrategy::Rename;
    
    let storage_manager = StorageManager::new(config.storage, Some(temp_dir.path().to_path_buf()));
    
    // 创建一个已存在的文件
    let existing_file = temp_dir.path().join("test.txt");
    fs::write(&existing_file, "existing content").await.unwrap();
    
    // 测试重命名策略
    let new_path = storage_manager.handle_file_conflict(&existing_file).await.unwrap();
    assert_ne!(new_path, existing_file);
    assert!(new_path.to_string_lossy().contains("test_1.txt"));
}

#[tokio::test]
async fn test_directory_creation() {
    let temp_dir = TempDir::new().unwrap();
    let config = create_test_config();
    let storage_manager = StorageManager::new(config.storage, Some(temp_dir.path().to_path_buf()));
    
    let test_path = temp_dir.path().join("deep/nested/directory/file.txt");
    
    // 确保目录被创建
    storage_manager.ensure_directory(&test_path).await.unwrap();
    
    // 验证目录存在
    assert!(test_path.parent().unwrap().exists());
}

#[tokio::test]
async fn test_config_file_operations() {
    let temp_dir = TempDir::new().unwrap();
    let config_path = temp_dir.path().join("test_config.yaml");
    
    // 测试配置文件不存在时的行为
    let result = Config::load(&config_path).await;
    assert!(result.is_err());
    
    // 验证示例配置文件被创建
    let example_path = format!("{}.example", config_path.display());
    assert!(PathBuf::from(&example_path).exists());
    
    // 创建有效的配置文件
    let config = create_test_config();
    let config_content = serde_yaml::to_string(&config).unwrap();
    fs::write(&config_path, config_content).await.unwrap();
    
    // 测试配置文件加载
    let loaded_config = Config::load(&config_path).await.unwrap();
    assert_eq!(loaded_config.auth.cookie, "test_cookie");
    assert_eq!(loaded_config.download.max_concurrent, 2);
}

#[test]
fn test_tag_logic_parsing() {
    assert_eq!("and".parse::<TagLogic>().unwrap(), TagLogic::And);
    assert_eq!("or".parse::<TagLogic>().unwrap(), TagLogic::Or);
    assert_eq!("与".parse::<TagLogic>().unwrap(), TagLogic::And);
    assert_eq!("或".parse::<TagLogic>().unwrap(), TagLogic::Or);
    
    assert!("invalid".parse::<TagLogic>().is_err());
}

#[test]
fn test_artwork_type_display() {
    assert_eq!(ArtworkType::Illust.to_string(), "插画");
    assert_eq!(ArtworkType::Manga.to_string(), "漫画");
    assert_eq!(ArtworkType::Novel.to_string(), "小说");
}
