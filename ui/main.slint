// Pixiv Tag Downloader 主界面
import { Button, LineEdit, CheckBox, ComboBox, ScrollView, ListView, ProgressIndicator, TabWidget, GroupBox } from "std-widgets.slint";

// 下载项目结构
export struct DownloadItem {
    filename: string,
    status: string,
    progress: float,
    size: string,
}

// 标签项目结构
export struct TagItem {
    name: string,
    selected: bool,
}

// 主窗口
export component MainWindow inherits Window {
    title: "Pixiv Tag Downloader";
    width: 1000px;
    height: 700px;
    
    // 回调函数
    callback fetch-user-info(string);
    callback start-download();
    callback cancel-download();
    callback open-settings();
    callback tag-selection-changed(int, bool);
    callback artwork-type-changed(int, bool);
    
    // 属性
    in-out property <string> uid-input: "";
    in-out property <string> username: "";
    in-out property <string> status-message: "就绪";
    in-out property <bool> is-fetching: false;
    in-out property <bool> is-downloading: false;
    in-out property <float> overall-progress: 0.0;
    in-out property <[TagItem]> tags: [];
    in-out property <[DownloadItem]> download-items: [];
    in-out property <string> tag-logic: "and";
    in-out property <bool> illust-enabled: true;
    in-out property <bool> manga-enabled: true;
    in-out property <bool> novel-enabled: true;
    in-out property <string> log-text: "";
    
    VerticalLayout {
        padding: 16px;
        spacing: 12px;
        
        // 标题栏
        HorizontalLayout {
            Text {
                text: "Pixiv Tag Downloader";
                font-size: 24px;
                font-weight: 700;
                color: #2563eb;
            }
            
            Rectangle { }
            
            Button {
                text: "设置";
                width: 80px;
                clicked => { root.open-settings(); }
            }
        }
        
        // 主要内容区域
        TabWidget {
            Tab {
                title: "下载";
                
                VerticalLayout {
                    spacing: 12px;
                    
                    // 用户输入区域
                    GroupBox {
                        title: "用户信息";
                        
                        VerticalLayout {
                            spacing: 8px;
                            
                            HorizontalLayout {
                                Text {
                                    text: "用户 UID:";
                                    width: 80px;
                                    vertical-alignment: center;
                                }
                                
                                LineEdit {
                                    text <=> root.uid-input;
                                    placeholder-text: "请输入 Pixiv 用户 UID";
                                    enabled: !root.is-fetching && !root.is-downloading;
                                }
                                
                                Button {
                                    text: root.is-fetching ? "获取中..." : "获取信息";
                                    width: 100px;
                                    enabled: !root.is-fetching && !root.is-downloading && root.uid-input != "";
                                    clicked => { root.fetch-user-info(root.uid-input); }
                                }
                            }
                            
                            if root.username != "" : HorizontalLayout {
                                Text {
                                    text: "用户名:";
                                    width: 80px;
                                    vertical-alignment: center;
                                }
                                
                                Text {
                                    text: root.username;
                                    color: #059669;
                                    font-weight: 600;
                                }
                            }
                        }
                    }
                    
                    // 筛选区域
                    if root.tags.length > 0 : GroupBox {
                        title: "筛选条件";
                        
                        HorizontalLayout {
                            spacing: 16px;
                            
                            // 标签选择
                            VerticalLayout {
                                width: 40%;
                                
                                HorizontalLayout {
                                    Text {
                                        text: "标签选择:";
                                        font-weight: 600;
                                    }
                                    
                                    Rectangle { }
                                    
                                    Button {
                                        text: "全选";
                                        width: 60px;
                                        height: 24px;
                                    }
                                    
                                    Button {
                                        text: "清空";
                                        width: 60px;
                                        height: 24px;
                                    }
                                }
                                
                                ScrollView {
                                    height: 200px;
                                    
                                    VerticalLayout {
                                        for tag[index] in root.tags : CheckBox {
                                            text: tag.name;
                                            checked: tag.selected;
                                            toggled => { root.tag-selection-changed(index, self.checked); }
                                        }
                                    }
                                }
                            }
                            
                            // 筛选逻辑和作品类型
                            VerticalLayout {
                                width: 30%;
                                spacing: 16px;
                                
                                VerticalLayout {
                                    Text {
                                        text: "标签逻辑:";
                                        font-weight: 600;
                                    }
                                    
                                    ComboBox {
                                        model: ["与 (AND)", "或 (OR)"];
                                        current-value: root.tag-logic == "and" ? "与 (AND)" : "或 (OR)";
                                        selected => {
                                            root.tag-logic = self.current-index == 0 ? "and" : "or";
                                        }
                                    }
                                }
                                
                                VerticalLayout {
                                    Text {
                                        text: "作品类型:";
                                        font-weight: 600;
                                    }
                                    
                                    CheckBox {
                                        text: "插画";
                                        checked <=> root.illust-enabled;
                                        toggled => { root.artwork-type-changed(0, self.checked); }
                                    }
                                    
                                    CheckBox {
                                        text: "漫画";
                                        checked <=> root.manga-enabled;
                                        toggled => { root.artwork-type-changed(1, self.checked); }
                                    }
                                    
                                    CheckBox {
                                        text: "小说";
                                        checked <=> root.novel-enabled;
                                        toggled => { root.artwork-type-changed(2, self.checked); }
                                    }
                                }
                            }
                        }
                    }
                    
                    // 下载控制
                    HorizontalLayout {
                        Button {
                            text: root.is-downloading ? "下载中..." : "开始下载";
                            enabled: !root.is-downloading && root.username != "";
                            clicked => { root.start-download(); }
                        }
                        
                        if root.is-downloading : Button {
                            text: "取消下载";
                            clicked => { root.cancel-download(); }
                        }
                        
                        Rectangle { }
                        
                        Text {
                            text: root.status-message;
                            vertical-alignment: center;
                        }
                    }
                    
                    // 进度显示
                    if root.is-downloading : VerticalLayout {
                        spacing: 8px;
                        
                        HorizontalLayout {
                            Text {
                                text: "总体进度:";
                                width: 80px;
                            }
                            
                            ProgressIndicator {
                                progress: root.overall-progress;
                            }
                            
                            Text {
                                text: Math.round(root.overall-progress * 100) + "%";
                                width: 50px;
                            }
                        }
                        
                        ScrollView {
                            height: 200px;
                            
                            ListView {
                                for item in root.download-items : Rectangle {
                                    height: 30px;
                                    border-width: 1px;
                                    border-color: #e5e7eb;
                                    
                                    HorizontalLayout {
                                        padding: 8px;
                                        
                                        Text {
                                            text: item.filename;
                                            width: 40%;
                                            overflow: elide;
                                        }
                                        
                                        Text {
                                            text: item.status;
                                            width: 20%;
                                            color: item.status == "完成" ? #059669 : 
                                                   item.status == "失败" ? #dc2626 : #6b7280;
                                        }
                                        
                                        ProgressIndicator {
                                            width: 30%;
                                            progress: item.progress;
                                        }
                                        
                                        Text {
                                            text: item.size;
                                            width: 10%;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            
            Tab {
                title: "日志";
                
                ScrollView {
                    Text {
                        text: root.log-text;
                        font-family: "monospace";
                        font-size: 12px;
                        wrap: word-wrap;
                    }
                }
            }
        }
    }
}

// 设置窗口
export component SettingsWindow inherits Window {
    title: "设置";
    width: 600px;
    height: 500px;
    
    // 回调函数
    callback save-settings();
    callback cancel-settings();
    
    // 属性
    in-out property <string> cookie: "";
    in-out property <string> output-dir: "";
    in-out property <int> max-concurrent: 3;
    in-out property <int> delay-min: 1000;
    in-out property <int> delay-max: 3000;
    in-out property <bool> enable-resume: true;
    
    VerticalLayout {
        padding: 16px;
        spacing: 12px;
        
        Text {
            text: "设置";
            font-size: 20px;
            font-weight: 700;
        }
        
        ScrollView {
            VerticalLayout {
                spacing: 16px;
                
                GroupBox {
                    title: "认证设置";
                    
                    VerticalLayout {
                        spacing: 8px;
                        
                        Text {
                            text: "Pixiv Cookie:";
                            font-weight: 600;
                        }
                        
                        LineEdit {
                            text <=> root.cookie;
                            placeholder-text: "请输入从浏览器获取的 Pixiv Cookie";
                        }
                    }
                }
                
                GroupBox {
                    title: "下载设置";
                    
                    VerticalLayout {
                        spacing: 12px;
                        
                        HorizontalLayout {
                            Text {
                                text: "输出目录:";
                                width: 120px;
                                vertical-alignment: center;
                            }
                            
                            LineEdit {
                                text <=> root.output-dir;
                                placeholder-text: "./downloads";
                            }
                        }
                        
                        HorizontalLayout {
                            Text {
                                text: "最大并发数:";
                                width: 120px;
                                vertical-alignment: center;
                            }
                            
                            LineEdit {
                                text: root.max-concurrent;
                                input-type: number;
                                edited => {
                                    root.max-concurrent = self.text.to-float();
                                }
                            }
                        }
                        
                        HorizontalLayout {
                            Text {
                                text: "延迟范围 (ms):";
                                width: 120px;
                                vertical-alignment: center;
                            }
                            
                            LineEdit {
                                text: root.delay-min;
                                input-type: number;
                                width: 80px;
                                edited => {
                                    root.delay-min = self.text.to-float();
                                }
                            }
                            
                            Text {
                                text: " - ";
                                vertical-alignment: center;
                            }
                            
                            LineEdit {
                                text: root.delay-max;
                                input-type: number;
                                width: 80px;
                                edited => {
                                    root.delay-max = self.text.to-float();
                                }
                            }
                        }
                        
                        CheckBox {
                            text: "启用断点续传";
                            checked <=> root.enable-resume;
                        }
                    }
                }
            }
        }
        
        HorizontalLayout {
            Rectangle { }
            
            Button {
                text: "取消";
                clicked => { root.cancel-settings(); }
            }
            
            Button {
                text: "保存";
                clicked => { root.save-settings(); }
            }
        }
    }
}
